# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist
tmp
/out-tsc
out-tsc
/out/
/libs/svix/schemas

# dependencies
node_modules
.pnp
.pnp.js

# next.js
.next

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.iml

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# typescript
*.tsbuildinfo
next-env.d.ts

# misc
/.sass-cache
/connect.lock
/libpeerconnection.log
/typings
/.yarn

# debug
npm-debug.log
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
testem.log

# test
/coverage
/lint-reports
lcov.info
*.local.spec.ts

# env
.env
.env*.local
.env*.development
.env*.production
.env*.slsDeploy
.env*.slsRemove
.env*.build
.env*.serve
.env*.seed
.env*.dev
nx-cloud.env

# vercel
.vercel

# System Files
.DS_Store
*.pem
Thumbs.db
.nx-container

# npm
# project relies on `yarn.lock` generated by `yarn install`, so `package-lock.json` is not needed.
package-lock.json

# sonar
.scannerwork

# Cypress secrets and artifacts
apps/ui-partner-portal-e2e/cypress.env.json
apps/ui-partner-portal-e2e/src/videos
apps/ui-partner-portal-e2e/src/reports
apps/ui-partner-portal-e2e/src/fixtures/C*.json
apps/ui-partner-portal-e2e/src/fixtures/new*.json
apps/ui-partner-portal-e2e/cypress
apps/ui-hosted-application-e2e/cypress.env.json
apps/ui-hosted-application-e2e/src/videos
apps/ui-hosted-application-e2e/src/reports
apps/ui-hosted-application-e2e/src/fixtures/C*.json
apps/ui-hosted-application-e2e/cypress
apps/ui-merchant-portal-e2e/cypress.env.json
apps/ui-merchant-portal-e2e/src/videos
apps/ui-merchant-portal-e2e/src/reports
apps/ui-merchant-portal-e2e/src/fixtures/C*.json
apps/ui-merchant-portal-e2e/cypress

.serverless
.esbuild

# NewRelic logs
newrelic_agent.log

# AWS deploy files
cdk.out/
cdk.context.json
dockerexample.txt

*.bak
.nx
migrate.js
migrations.json


.history

*.local.spec.ts
nx-cloud.env

# Nix
.data
.direnv

.localstack
vitest.config.mts.timestamp-*.mjs
vite.config.mts.timestamp-*.mjs
# Devenv
.devenv*
devenv.local.nix

# direnv
.direnv

# pre-commit
.pre-commit-config.yaml

tools/volume/**

# event-registry artifacts defined at the libs level
# are ignored. All events should be directly defined
# at the `apps` level or copied to the `apps` level.
libs/**/events/@typespec

# Wiretap
wiretap-report.json

vite.config.*.timestamp*
vitest.config.*.timestamp*


# Python
**/__pycache__/*
.cache
**/.venv/*

.tshy
.tshy-build

# Playwright plugin
.auth
temp

.cursor
