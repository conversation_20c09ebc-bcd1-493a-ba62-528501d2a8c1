{"_COMMENT": "only used by shadcn/ui cli to generate shadcn/ui components", "extends": "./tsconfig.base.json", "files": [], "references": [{"path": "./libs/account-service-client"}, {"path": "./libs/account-service-types"}, {"path": "./libs/accounts-db"}, {"path": "./libs/backend-kit"}, {"path": "./libs/cardpointe"}, {"path": "./libs/copilot-fiserv"}, {"path": "./libs/core-db"}, {"path": "./libs/core-types"}, {"path": "./libs/cross-river-sdk"}, {"path": "./libs/danger"}, {"path": "./libs/errors"}, {"path": "./libs/fastify"}, {"path": "./libs/flagsmith"}, {"path": "./libs/fwdevents"}, {"path": "./libs/grailpay-sdk"}, {"path": "./libs/instrumentation/fastify"}, {"path": "./libs/instrumentation/nextjs"}, {"path": "./libs/next-sessions"}, {"path": "./libs/payment-gateway-proxy"}, {"path": "./libs/plaid-sdk"}, {"path": "./libs/risk"}, {"path": "./libs/serverless-http"}, {"path": "./libs/service-auth"}, {"path": "./libs/service-client-library"}, {"path": "./libs/snowflake-settlement"}, {"path": "./libs/svix"}, {"path": "./libs/tailwind-components"}, {"path": "./libs/tailwind-utils"}, {"path": "./libs/tax-bit-library"}, {"path": "./libs/ticketing-system"}, {"path": "./libs/utils"}, {"path": "./libs/vault-client"}, {"path": "./libs/web-elements-sdk"}, {"path": "./libs/zod-types"}, {"path": "./libs/zod-types-common"}, {"path": "./libs/zod-types-ui"}, {"path": "./apps/api-key-service"}, {"path": "./apps/analytics-service"}, {"path": "./apps/boarding-service"}, {"path": "./apps/core-event-processors"}, {"path": "./apps/decisioning-event-processor"}, {"path": "./apps/decisioning-service"}, {"path": "./apps/file-service"}, {"path": "./apps/payments-core-service"}, {"path": "./apps/terminals"}, {"path": "./apps/ticketing-service"}, {"path": "./apps/ui-hosted-application"}, {"path": "./apps/ui-merchant-portal"}, {"path": "./apps/ui-partner-portal"}, {"path": "./apps/ui-partner-portal-v2"}, {"path": "./libs/unmatched-settlement/modules/unmatched-settlement-fastify"}, {"path": "./libs/unmatched-settlement/modules/unmatched-settlement-common"}, {"path": "./libs/unmatched-settlement/modules/unmatched-settlement-types"}, {"path": "./libs/terminals-service/modules/terminals-fastify"}, {"path": "./libs/terminals-service/modules/terminals-common"}, {"path": "./libs/terminals-service/modules/terminals-types"}, {"path": "./libs/service-types/residuals-service-types"}, {"path": "./libs/analytics/modules/analytics-postgres"}, {"path": "./apps/ui-hosted-application-playwright-e2e"}, {"path": "./libs/analytics/modules/analytics-fastify"}, {"path": "./libs/analytics/modules/analytics-common"}, {"path": "./libs/analytics/modules/analytics-types"}, {"path": "./libs/reporting/merchant-applications"}, {"path": "./libs/reporting/merchant-accounts"}, {"path": "./libs/opentelemetry-lambda-layer"}, {"path": "./libs/service-types/vault-types"}, {"path": "./libs/service-types/risk-types"}, {"path": "./libs/reporting/common/server"}, {"path": "./libs/reporting/payout-events"}, {"path": "./libs/serverless/environments"}, {"path": "./libs/reporting/transactions"}, {"path": "./apps/serverless-partner-api"}, {"path": "./libs/reporting/contractors"}, {"path": "./apps/serverless-settlement"}, {"path": "./libs/reporting/businesses"}, {"path": "./libs/bank-gateway-proxy"}, {"path": "./libs/reporting/accounts"}, {"path": "./libs/reporting/disputes"}, {"path": "./libs/reporting/partners"}, {"path": "./libs/reporting/payments"}, {"path": "./libs/playwright/common"}, {"path": "./libs/reporting/openapi"}, {"path": "./libs/reporting/payouts"}, {"path": "./libs/reporting/refunds"}, {"path": "./libs/serverless/logger"}, {"path": "./libs/reporting/users"}, {"path": "./apps/serverless-core"}, {"path": "./libs/playwright/hap"}, {"path": "./libs/reporting/auth"}, {"path": "./libs/playwright/pp"}, {"path": "./libs/ui/components"}, {"path": "./apps/vault-service"}, {"path": "./libs/forward-spec"}, {"path": "./libs/ui/providers"}, {"path": "./libs/ui/services"}, {"path": "./libs/ui/styles"}, {"path": "./libs/ui/hooks"}, {"path": "./libs/ui/pages"}, {"path": "./libs/ui/types"}, {"path": "./libs/ui/utils"}, {"path": "./apps/risk-service"}, {"path": "./apps/ui-hosted-application-e2e"}, {"path": "./apps/ui-partner-portal-v2-e2e"}, {"path": "./libs/shared/cypress-commands"}, {"path": "./apps/ui-merchant-portal-e2e"}, {"path": "./apps/ui-partner-portal-e2e"}, {"path": "./apps/ui-hosted-checkout-page"}, {"path": "./apps/residuals-service"}, {"path": "./libs/ui/features/merchant-applications"}, {"path": "./libs/ui/features/transactions"}, {"path": "./libs/ui/features/contractors"}, {"path": "./apps/serverless-decisioning"}, {"path": "./libs/ui/features/businesses"}, {"path": "./libs/ui/features/data-table"}, {"path": "./libs/ui/features/merchants"}, {"path": "./libs/ui/features/residuals"}, {"path": "./libs/ui/features/accounts"}, {"path": "./libs/ui/features/disputes"}, {"path": "./libs/ui/features/partners"}, {"path": "./libs/ui/features/payments"}, {"path": "./libs/ui/features/payouts"}, {"path": "./libs/ui/features/refunds"}, {"path": "./libs/ui/features/tenants"}, {"path": "./libs/ui/features/common"}, {"path": "./libs/ui/features/forms"}, {"path": "./libs/ui/features/users"}, {"path": "./apps/ui-merchant-portal-v2"}, {"path": "./libs/ui/features/charts"}, {"path": "./libs/fee/modules/fee-service-types"}, {"path": "./libs/fee/modules/fee-fastify"}, {"path": "./libs/fee/modules/fee-common"}, {"path": "./libs/fee/modules/fee-types"}, {"path": "./libs/ledger-account/modules/ledger-account-types"}, {"path": "./libs/ledger-account/modules/ledger-account-service-types"}, {"path": "./libs/ledger-account/modules/ledger-account-fastify"}, {"path": "./libs/ledger-account/modules/ledger-account-common"}, {"path": "./apps/ui-merchant-portal-v2-e2e"}, {"path": "./libs/playwright/mp"}, {"path": "./libs/analytics/modules/analytics-dynamo"}, {"path": "./libs/notifications/rest-contracts"}, {"path": "./libs/notifications/email-service"}, {"path": "./apps/notifications-service"}, {"path": "./libs/notifications/fastify"}, {"path": "./libs/notifications/types"}, {"path": "./libs/notifications/lib"}, {"path": "./apps/serverless-notifications"}]}