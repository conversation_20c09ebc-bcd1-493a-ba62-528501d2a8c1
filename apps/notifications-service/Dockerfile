# -----------------------------------------------------------------------------
# Step: Base image
# -----------------------------------------------------------------------------
FROM oven/bun:1.2.15 AS base
WORKDIR /app

# -----------------------------------------------------------------------------
# Step: Install dependencies (optimized for caching)
# -----------------------------------------------------------------------------
FROM base AS deps
COPY --chown=bun:bun apps/notifications-service/package.json ./
# Uncomment if you have a bun.lockb for better caching
# COPY --chown=bun:bun apps/notifications-service/bun.lockb ./
RUN sed -i '/workspace\:/d' ./package.json
RUN bun install --no-lockfile

# -----------------------------------------------------------------------------
# Step: Build artifacts
# -----------------------------------------------------------------------------
FROM base AS build
COPY --from=deps /app/node_modules ./node_modules
COPY --chown=bun:bun dist/apps/notifications-service ./
COPY --chown=bun:bun dist/libs/instrumentation/fastify/instrumentation.cjs ./
# Copy any other necessary files here

# -----------------------------------------------------------------------------
# Step: Create deployed artifact (final image)
# -----------------------------------------------------------------------------
FROM base AS runner
LABEL org.opencontainers.image.source="https://github.com/dbdventures/core-microservices-nx/notifications-service"

USER root
RUN apt-get update && apt-get install -y tini && rm -rf /var/lib/apt/lists/*
USER bun

WORKDIR /app
COPY --from=build --chown=bun:bun /app /app

ENV NODE_ENV=production
ENV NODE_OPTIONS="max-old-space-size=375"
ARG BUILD_VERSION=unknown
ENV BUILD_VERSION=$BUILD_VERSION

EXPOSE 3000
ENTRYPOINT ["/usr/bin/tini", "--"]
CMD ["bun", "run", "-r", "./instrumentation.cjs", "main.js"]  