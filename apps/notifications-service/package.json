{"name": "@notifications/service", "version": "0.0.1", "private": true, "nx": {"name": "notifications-service", "targets": {"build": {"executor": "nx:run-commands", "options": {"cwd": "apps/notifications-service", "command": "bun build.ts"}}, "serve": {"continuous": false, "executor": "nx:run-commands", "defaultConfiguration": "development", "dependsOn": ["^build"], "options": {"cwd": "apps/notifications-service", "command": "bun --watch run src/main.ts"}, "configurations": {"development": {}, "production": {}}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build", "fastify-instrumentation:build"], "options": {"file": "./apps/notifications-service/Dockerfile", "platforms": ["linux/arm64"], "cache-from": ["type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/notifications-service:cache"], "cache-to": ["mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/notifications-service:cache"], "metadata": {"images": ["528973710517.dkr.ecr.us-east-1.amazonaws.com/notifications-service"], "tags": ["type=sha,format=short", "type=ref,event=tag"]}}, "configurations": {"development": {"push": false, "load": true}, "production": {"push": true}}}}}, "devDependencies": {"@notifications/fastify": "workspace:*", "@dbd/fastify": "workspace:*", "@dbd/errors": "workspace:*"}, "dependencies": {"dynamodb-toolbox": "2.2.1", "fastify": "4.29.1", "@fastify/swagger-ui": "3.0.0", "pino": "9.6.0"}}