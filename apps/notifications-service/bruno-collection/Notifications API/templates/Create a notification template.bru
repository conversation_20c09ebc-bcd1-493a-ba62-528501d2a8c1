meta {
  name: Create a notification template
  type: http
  seq: 1
}

post {
  url: {{baseUrl}}/notification_templates
  body: json
  auth: none
}

headers {
  x-tenant-id: {{tenantId}}
}

body:json {
  {
    "notificationType": "document_request",  
    "title": "We need some more documents",
    "content": "Please email us some stuff",
    "channels": {
      "email": {
        "subject": "Foo Bar",
        "htmlTemplate": "<p>{{foo}} Bar</p>",
        "plainTextTemplate": "Hello Man",
        "externalTemplateId": "d-867d39b347eb42fbb9d64bcc0d9f2ff7"
      }
    }  
  }
}

script:post-response {
  if(res.getStatus() == 200){
  bru.setVar('notTemplate1',res.getBody().notificationType)
  }
}

tests {
  test("should be able to create a notifications", function () {
    expect(res.getStatus()).to.equal(200);
  });
   
  test("should return proper notification", function () {
    const body = res.getBody()
    expect(body.templateType).to.eq('tenant');
  });
}
