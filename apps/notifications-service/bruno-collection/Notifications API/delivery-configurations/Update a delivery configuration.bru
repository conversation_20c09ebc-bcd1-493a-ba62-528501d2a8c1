meta {
  name: Update a delivery configuration
  type: http
  seq: 4
}

patch {
  url: {{baseUrl}}/delivery_configurations
  body: json
  auth: none
}

headers {
  x-tenant-id: {{tenantId}}
}

body:json {
  {
    "configurationType": "tenant",
    "notificationsEnabled": true,
    "channels": {
      "email": {
        "enabled": true,      
        "provider": "sendgrid",
        "config": {
          "sendgrid": {
            "apiKey":"{{sendGridKey}}",
            "fromEmail": "<EMAIL>",
            "fromName": "Foward Support",
            "replyTo": "<EMAIL>"
          },
          "smtp": {
            "host": "smtp.yahoo.net",
            "port": "438",
            "auth": {
              "user": "user 222",
              "pass": "password-shhhh"
            },
            "fromEmail": "<EMAIL>",
            "fromName": "Foo Bar 2",
            "replyTo": "<EMAIL>"
          }        
        }
      }
    }
  }
}

tests {
  test("should be able to update a delivery config", function () {
    expect(res.getStatus()).to.equal(200);
  });
   
  test("should return updated host", function () {
    expect(res.getBody().channels.email.config.smtp.host).to.eq('smtp.yahoo.net');
  });
}
