meta {
  name: Create a delivery configuration partner
  type: http
  seq: 5
}

post {
  url: {{baseUrl}}/delivery_configurations
  body: json
  auth: none
}

headers {
  x-tenant-id: {{tenantId}}
  x-partner-id: {{partnerId}}
}

body:json {
  {  
    "notificationsEnabled": true,
    "channels": {
      "email": {
        "enabled": true,
        "usePartnerConfig": true,
        "provider": "sendgrid",
        "config": {
          "sendgrid": {
            "apiKey":"{{sendGridKey}}",
            "fromEmail": "<EMAIL>",
            "fromName": "Foward Support",
            "replyTo": "<EMAIL>"
          }        
        }
      }
    }
  }
}

script:post-response {
  if(res.getStatus()==200)
    bru.setVar('partnerDelConfig',res.getBody().id)
}

tests {
  test("should be able to create delivery config for partner", function () {
    expect(res.getStatus()).to.equal(200);
  });
   
  test("should return partner config type", function () {
    expect(res.getBody().configurationType).to.eq('partner');
  });
}
