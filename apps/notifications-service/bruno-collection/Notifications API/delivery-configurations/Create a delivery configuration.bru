meta {
  name: Create a delivery configuration
  type: http
  seq: 1
}

post {
  url: {{baseUrl}}/delivery_configurations
  body: json
  auth: none
}

headers {
  x-tenant-id: {{tenantId}}
}

body:json {
  {
    "notificationsEnabled": true,
    "channels": {
      "email": {
        "enabled": true,      
        "provider": "sendgrid",
        "config": {
          "sendgrid": {
            "apiKey":"{{sendGridKey}}",
            "fromEmail": "<EMAIL>",
            "fromName": "Foward Support",
            "replyTo": "<EMAIL>"
          },
          "smtp": {
            "host": "smtp.yahoo.comm",
            "port": "437",
            "auth": {
              "user": "user",
              "pass": "password-shhhh"
            },
            "fromEmail": "<EMAIL>",
            "fromName": "Foo Bar",
            "replyTo": "<EMAIL>"
          }        
        }
      }
    }
  }
}

script:post-response {
  if(res.getStatus()==200)
    bru.setVar('delConfig',res.getBody().id)
}

tests {
  test("should be able to create delivery config", function () {
    expect(res.getStatus()).to.equal(200);
  });
   
  test("should return json", function () {
    expect(res.getBody().configurationType).to.eq('tenant');
  });
}
