meta {
  name: Get a delivery configuration partner
  type: http
  seq: 6
}

get {
  url: {{baseUrl}}/delivery_configurations
  body: none
  auth: none
}

headers {
  x-partner-id: {{partnerId}}
  ~x-tenant-id: {{tenantId}}
}

body:json {
  {
    "configurationType": "",
    "notificationsEnabled": "",
    "channels": {
      "email": {
        "enabled": "",
        "usePartnerConfig": "",
        "provider": "",
        "config": {
          "smtp": {
            "host": "",
            "port": "",
            "auth": {
              "user": "",
              "pass": ""
            },
            "fromEmail": "",
            "fromName": "",
            "replyTo": ""
          },
          "sendgrid": {
            "apiKey": "",
            "fromEmail": "",
            "fromName": "",
            "replyTo": ""
          }
        }
      }
    }
  }
}

tests {
  test("should be able to fetch a delivery config", function () {
    expect(res.getStatus()).to.equal(200);
  });
   
  test("should return json", function () {
    expect(res.getBody().configurationType).to.eq('partner');
  });
  
  test("should return id", function () {
    expect(res.getBody().id).to.eq(bru.getVar('partnerDelConfig'));
  });
}
