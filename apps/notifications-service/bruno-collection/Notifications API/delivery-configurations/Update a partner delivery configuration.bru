meta {
  name: Update a partner delivery configuration
  type: http
  seq: 7
}

patch {
  url: {{baseUrl}}/delivery_configurations
  body: json
  auth: none
}

headers {
  x-tenant-id: {{tenantId}}
  x-partner-id: {{partnerId}}
}

body:json {
  {  
    "notificationsEnabled": true,
    "channels": {
      "email": {
        "enabled": true,
        "usePartnerConfig": true,
        "provider": "smtp",
        "config": {
          "sendgrid": {
            "apiKey":"{{sendGridKey}}",
            "fromEmail": "<EMAIL>",
            "fromName": "Foward Support",
            "replyTo": "<EMAIL>"
          },
          "smtp": {
            "host": "smtp.sendgrid.net",
            "port": "465",
            "auth": {
              "user": "apikey",
              "pass": "{{sendGridKey}}"
            },
            "fromEmail": "<EMAIL>",
            "fromName": "Forward Support",
            "replyTo": "<EMAIL>"
          }
        }
      }
    }
  }
}

script:post-response {
  if(res.getStatus()==200)
    bru.setVar('partnerDelConfig',res.getBody().id)
}

tests {
  test("should be able to create delivery config for partner", function () {
    expect(res.getStatus()).to.equal(200);
  });
   
  test("should return partner config type", function () {
    expect(res.getBody().configurationType).to.eq('partner');
    expect(res.body.channels.email.config.sendgrid.fromEmail).to.eq('<EMAIL>');
  });
}
