{"name": "serverless-notifications", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/serverless-notifications/src", "tags": ["serverless-notifications", "serverless"], "implicitDependencies": [], "targets": {"tsc": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/out-tsc"], "options": {"cwd": "apps/serverless-notifications", "commands": ["tsc --build --emitDeclarationOnly"]}}, "serve": {"executor": "nx:run-commands", "options": {"cwd": "apps/serverless-notifications", "color": true, "command": "TS_NODE_PROJECT='./tsconfig.app.json' sls offline start --stage development"}}, "slsBuild": {"executor": "nx:run-commands", "defaultConfiguration": "development", "options": {"cwd": "apps/serverless-notifications", "color": true, "command": "echo 'see configurations'"}, "configurations": {"development": {"command": "SLS_DEBUG=1 serverless package --verbose --stage development"}, "sandbox": {"command": "SLS_DEBUG=1 serverless package --verbose --stage sandbox"}, "production": {"command": "SLS_DEBUG=1 serverless package --verbose --stage production"}}}, "slsDeploy": {"executor": "nx:run-commands", "options": {"cwd": "apps/serverless-notifications", "color": true, "parallel": false}, "configurations": {"development": {"command": "TS_NODE_PROJECT='./tsconfig.app.json' doppler run -p nx_core -c dev2_serverless_notifications -- sls deploy --stage development --verbose"}, "sandbox": {"command": "TS_NODE_PROJECT='./tsconfig.app.json' doppler run -p nx_core -c cs_serverless_notifications -- sls deploy --stage sandbox --verbose"}, "production": {"command": "TS_NODE_PROJECT='./tsconfig.app.json' doppler run -p nx_core -c prd_serverless_notifications -- sls deploy --stage production --verbose"}}}}}