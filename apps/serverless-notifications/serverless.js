// @ts-check
/**
 * @typedef {import('@dbd/serverless-environments').Environment} Environment
 */

// This file has to be JS because of the serverless limitations when it comes to TS support and serverless config.

const { baseServerlessConfiguration, getOTELEnv } = require('@dbd/serverless-environments/base');
const { environments } = require('@dbd/serverless-environments');

/** @type {import('@dbd/serverless-environments').Environment} */
const env = environments[/** @type {keyof typeof environments} */ (process.env.AWS_ENV ?? 'development')];

/** @type {{ [key: string]: any }} */
const baseEnvironment = /** @type {{ [key: string]: any }} */ (
  baseServerlessConfiguration.provider && baseServerlessConfiguration.provider.environment
    ? baseServerlessConfiguration.provider.environment
    : {}
);

module.exports = (async () => {
  const serverlessConfig = {
    ...baseServerlessConfiguration,
    service: 'serverless-notifications',
    package: {
      individually: true,
      include: ['collector.yml'],
    },
    plugins: [
      'serverless-esbuild',
      'serverless-prune-plugin',
      'serverless-logging-config',
      'serverless-domain-manager',
    ],
    provider: {
      ...baseServerlessConfiguration.provider,
      environment: {
        ...baseEnvironment,
        ...getOTELEnv(`serverless-notifications`),
        CLIENT_ID: env.clientId,
        CLIENT_SECRET: env.clientSecret,
        FUSIONAUTH_URL: env.fusionAuthUrl,
        MICROSERVICE_MASTER_ENTITY_ID: env.microserviceMasterEntityId,
        NOTIFICATIONS_SERVICE_URL: env.notificationsServiceUrl,
      },
      layers: baseServerlessConfiguration?.provider?.layers ?? [],
      vpc: env.vpc,
      apiGateway: {
        apiKeys: [
          {
            name: 'taktile_notifications_api_key',
            value: '${env:TAKTILE_NOTIFICATIONS_API_KEY, "taketile_notifications_default"}',
          },
        ],
      },
    },
    custom: {
      ...baseServerlessConfiguration.custom,
      customDomains: env.customDomains.notifications,
      esbuild: {
        config: './esbuild.config.cjs',
      },
      warmup: { default: { enabled: false } },
      datadog: {},
    },
    functions: {
      processDeliveryEvents: {
        handler: './src/handlers.processDeliveryEvents',
        events: [
          {
            sqs: {
              arn: env.sqs && env.sqs.notificationDeliveryCreatedArn,
              batchSize: 1,
            },
          },
        ],
      },
      createNotification: {
        handler: './src/handlers.createNotification',
        events: [
          {
            http: {
              method: 'post',
              path: 'notifications',
              private: true,
            },
          },
        ],
      },
    },
    resources: {
      Resources: {},
    },
  };
  return serverlessConfig;
})();
