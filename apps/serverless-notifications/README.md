# Serverless Notifications

## Details

Basis handler for managent notificaton events.

## Deploy To AWS

```shell
BUILD_VERSION=latest AWS_REGION=us-east-1 aws-vault exec development-account -- bun nx run serverless-notifications:slsBuild:development
BUILD_VERSION=latest AWS_REGION=us-east-1 aws-vault exec development-account -- bun nx run serverless-notifications:slsDeploy:development
BUILD_VERSION=latest AWS_REGION=us-east-1 aws-vault exec development-account -- bun nx run serverless-notifications:slsDeploy-terminals:development
BUILD_VERSION=latest AWS_REGION=us-east-1 aws-vault exec customer-sandbox-account -- bun nx run serverless-notifications:slsDeploy:sandbox
BUILD_VERSION=latest AWS_REGION=us-east-1 aws-vault exec production-account -- bun nx run serverless-notifications:slsDeploy:production
```
