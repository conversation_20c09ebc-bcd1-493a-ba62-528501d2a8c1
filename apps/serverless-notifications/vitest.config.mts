import { defineConfig } from 'vitest/config';

export default defineConfig({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/serverless-notifications',

  plugins: [],

  resolve: {
    alias: {},
  },

  test: {
    coverage: {
      reportsDirectory: '../../coverage/apps/serverless-notifications',
      reporter: ['text', 'json', 'html', 'lcov'],
      provider: 'v8',
      enabled: true,
      include: ['src/**/*.ts'],
      exclude: ['vite.config.ts', 'src/**/*.spec.ts'],
    },
    environment: 'node',
    globals: true,
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    setupFiles: ['./vitest.setup.ts'],
    watch: false,
  },
});
