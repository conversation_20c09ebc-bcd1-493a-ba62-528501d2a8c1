{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "out-tsc/serverless-notifications", "types": ["node"], "rootDir": "src", "tsBuildInfoFile": "out-tsc/serverless-notifications/tsconfig.app.tsbuildinfo", "sourceMap": true, "moduleResolution": "bundler", "module": "ESNext"}, "include": ["src/**/*.ts"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"], "references": [{"path": "../../libs/notifications/rest-contracts/tsconfig.lib.json"}, {"path": "../../libs/serverless/logger/tsconfig.lib.json"}, {"path": "../../libs/service-auth/tsconfig.lib.json"}, {"path": "../../libs/notifications/types/tsconfig.lib.json"}, {"path": "../../libs/serverless/environments/tsconfig.lib.json"}]}