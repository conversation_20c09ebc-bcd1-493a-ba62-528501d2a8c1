import { beforeEach, describe, expect, it, vi } from 'vitest';

import { client, getAccessToken } from './notification-client';
import { handler } from './process-delivery-events';

// Mock dependencies
vi.mock('./notification-client', () => ({
  client: {
    notifications: {
      sendDelivery: vi.fn(),
    },
  },
  getAccessToken: vi.fn(),
}));

vi.mock('@dbd/serverless-logger', () => ({
  Logger: {
    info: vi.fn((..._args) => {}),
    error: vi.fn((..._args) => {}),
  },
  LogWithRequestTracing: vi.fn(),
}));

const sampleEvent = {
  specversion: '1.0',
  source: 'test-source',
  id: 'test-id',
  type: 'notification_delivery.created',
  datacontenttype: 'application/json',
  subject: 'test-subject',
  time: '2021-01-01T00:00:00.000Z',
  data: {
    object: {
      entity: 'notification_delivery',
      id: 'test-delivery-id',
      notificationId: 'test-notification-id',
      channel: 'email',
      status: 'pending',
      configurationId: 'test-configuration-id',
      channelProperties: {
        to: ['<EMAIL>'],
      },
      createdAt: '2021-01-01T00:00:00.000Z',
      updatedAt: '2021-01-01T00:00:00.000Z',
    },
  },
};

describe('process-delivery-events handler', () => {
  const mockContext = {} as any;
  const mockAccessToken = 'mock-access-token';

  beforeEach(() => {
    vi.clearAllMocks();
    (getAccessToken as any).mockResolvedValue(mockAccessToken);
  });

  it('should successfully process a valid message', async () => {
    const mockEvent = {
      Records: [
        {
          body: JSON.stringify(sampleEvent),
        },
      ],
    };

    const mockResponse = {
      status: 200,
      body: { success: true },
    };

    (client.notifications.sendDelivery as any).mockResolvedValue(mockResponse);

    await handler(mockEvent as any, mockContext as any);

    expect(getAccessToken).toHaveBeenCalled();
    expect(client.notifications.sendDelivery).toHaveBeenCalledWith({
      params: { id: 'test-notification-id' },
      body: { deliveryId: 'test-delivery-id' },
      headers: {
        authorization: `Bearer ${mockAccessToken}`,
        'x-tenant-id': expect.any(String),
      },
    });
  });

  it('should handle 404 response from notification service', async () => {
    const mockEvent = {
      Records: [
        {
          body: JSON.stringify(sampleEvent),
        },
      ],
    };

    const mockResponse = {
      status: 404,
      body: { error: 'Not found' },
    };

    (client.notifications.sendDelivery as any).mockResolvedValue(mockResponse);

    expect(handler(mockEvent as any, mockContext)).resolves.toBe('Delivery not found');
  });

  it('should handle invalid message format', async () => {
    const mockEvent = {
      Records: [
        {
          body: JSON.stringify({ invalid: 'format' }),
        },
      ],
    };

    await expect(handler(mockEvent as any, mockContext)).resolves.toBe('Zod validation error');
  });

  it('should handle API errors', async () => {
    const mockEvent = {
      Records: [
        {
          body: JSON.stringify(sampleEvent),
        },
      ],
    };

    const mockError = new Error('API Error');
    (client.notifications.sendDelivery as any).mockRejectedValue(mockError);

    await expect(handler(mockEvent as any, mockContext)).rejects.toThrow('API Error');
  });
});
