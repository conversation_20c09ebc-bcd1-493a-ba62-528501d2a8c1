import { Logger, LogWithRequestTracing } from '@dbd/serverless-logger';
import { CreateNotificationSchema } from '@notifications/types';
import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { ZodError } from 'zod';

import { client, getAccessToken } from './notification-client';

export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
  LogWithRequestTracing(event, context);
  Logger.info(event, 'Event received');

  try {
    const tenantId = event.headers['x-tenant-id'];
    if (!tenantId) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Missing x-tenant-id header' }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    // Parse and validate the request body
    const parsedBody = JSON.parse(event.body || '{}');
    const validatedNotification = CreateNotificationSchema.parse(parsedBody);

    const accessToken = await getAccessToken();

    // Forward the validated request to the notification service
    const response = await client.notifications.createNotification({
      params: {},
      body: validatedNotification,
      headers: {
        authorization: `Bearer ${accessToken}`,
        'x-tenant-id': tenantId,
        ...event.headers,
      },
    });

    return {
      statusCode: response.status,
      body: JSON.stringify(response.body),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    Logger.error(error, 'Error processing notification request');

    // Handle validation errors specifically
    if (error instanceof ZodError) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          error: 'Invalid request body',
          details: error.errors,
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
