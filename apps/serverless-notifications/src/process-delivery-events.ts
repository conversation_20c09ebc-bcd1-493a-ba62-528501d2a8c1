import { Logger, LogWithRequestTracing } from '@dbd/serverless-logger';
import { NotificationDeliveryCreatedEventSchema } from '@notifications/types';
import { Context, SQSEvent } from 'aws-lambda';
import { ZodError } from 'zod';

import { env } from './env';
import { client, getAccessToken } from './notification-client';

export const handler = async (event: SQSEvent, context: Context) => {
  LogWithRequestTracing(event, context);
  Logger.info(event, 'Event');
  try {
    // Process each SQS message
    for (const record of event.Records) {
      const message = record.body;

      Logger.info(message, 'Processing message');

      try {
        const parsedMessage = NotificationDeliveryCreatedEventSchema.parse(JSON.parse(message));
        const delivery = parsedMessage.data.object;
        const accessToken = await getAccessToken();
        const response = await client.notifications.sendDelivery({
          params: { id: delivery.notificationId },
          body: { deliveryId: delivery.id },
          headers: {
            authorization: `Bearer ${accessToken}`,
            'x-tenant-id': env.TENANT_ID,
          },
        });
        if (response.status === 200) {
          Logger.info(response.body, 'Delivery sent successfully');
        } else if (response.status === 404) {
          Logger.info(response.body, 'Delivery not found');
          return 'Delivery not found';
        } else {
          Logger.error(response.body, 'Failed to send delivery');
          throw new Error('Failed to send delivery non-200 error from service');
        }
      } catch (error) {
        if (error instanceof ZodError) {
          Logger.error(error.errors, 'Zod validation error');
          return 'Zod validation error';
        } else {
          Logger.error(error, 'Failed to process message');
          throw error;
        }
      }
    }
    return 'Success';
  } catch (error) {
    Logger.error(error, 'Error processing SQS event');
    throw error; // Re-throw to mark the Lambda execution as failed
  }
};
