import { ServerAuthClass } from '@dbd/service-auth';
import { contract } from '@notifications/rest-contracts';
import { initClient } from '@ts-rest/core';

import { env } from './env';

const serverAuth = new ServerAuthClass(
  env.CLIENT_ID,
  env.CLIENT_SECRET,
  env.FUSIONAUTH_URL,
  env.MICROSERVICE_MASTER_ENTITY_ID,
);

export const client = initClient(contract, {
  baseUrl: env.NOTIFICATIONS_SERVICE_URL,
});

export async function getAccessToken() {
  return await serverAuth.getAccessToken();
}
