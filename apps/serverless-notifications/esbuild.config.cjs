const { <PERSON>uffer } = require('node:buffer');
const fs = require('node:fs');
const path = require('node:path');
const { environments } = require('../../libs/serverless/environments/dist/index.js');
const env = environments[process.env.AWS_ENV ?? 'development'];

const baseServerlessConfiguration =
  require('../../libs/serverless/environments/dist/serverless.base.js').baseServerlessConfiguration;

// inspired by https://github.com/evanw/esbuild/issues/1685
const excludeVendorFromSourceMap = (includes = []) => ({
  name: 'excludeVendorFromSourceMap',
  setup(build) {
    const emptySourceMap =
      '\n//# sourceMappingURL=data:application/json;base64,' +
      Buffer.from(
        JSON.stringify({
          version: 3,
          sources: [''],
          mappings: 'A',
        }),
      ).toString('base64');

    build.onLoad({ filter: /node_modules/u }, async (args) => {
      if (
        /\.[mc]?js$/.test(args.path) &&
        !new RegExp(includes.join('|'), 'u').test(args.path.split(path.sep).join(path.posix.sep))
      ) {
        return {
          contents: `${await fs.promises.readFile(args.path, 'utf8')}${emptySourceMap}`,
          loader: 'default',
        };
      }
    });
  },
});

let exclude = baseServerlessConfiguration.custom.esbuild.exclude;
exclude = env.dataDogEnabled ? [...exclude, 'datadog-lambda-js', 'dd-trace'] : exclude;

module.exports = () => {
  return {
    format: 'cjs',
    bundle: true,
    minify: false,
    target: 'esnext',
    packager: 'yarn', // Have to use yarn here until https://github.com/floydspace/serverless-esbuild/issues/509 is fixed
    sourcemap: true,
    sourcesContent: false,
    keepNames: false,
    outputFileExtension: '.js',
    resolveExtensions: ['.ts', '.js', '.mjs'],
    external: [],
    // exclude: [...(baseServerlessConfiguration.custom?.esbuild?.exclude ?? ''),'@fastify/swagger'],
    // https://github.com/sveltejs/kit/issues/8864  bundling aws because of this issue
    exclude: exclude,
    tsconfig: 'tsconfig.app.json',
    plugins: [excludeVendorFromSourceMap([])],
    loader: { '.node': 'copy' },
  };
};
