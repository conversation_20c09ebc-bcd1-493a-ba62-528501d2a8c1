# ui-partner-portal-v2

## Local Development

Create a .env.local file in the root of the project and copy the .env.example file:
```
cp .env.example .env.local
```

Start the localstack docker container:

```
docker compose -f ./tools/docker-compose.localstack.yaml up -d
```

Run the app:

```
nx dev ui-partner-portal-v2
```

Open http://localhost:3000 in your browser.

## Ladle component library

To preview available components from the design system, run:

```
nx ladle:serve ui-partner-portal-v2
```

Ladle configuration is located in the `./.ladle` directory.

# Running Container Locally
You need to set POD_IP=host.docker.internal to make it work with the below command
```shell
bun nx run ui-partner-portal-v2:container:development
docker run --env-file apps/ui-partner-portal-v2/.env.local -p 3000:3000 ui-partner-portal-v2:latest
```