{"name": "@dbd/ui-partner-portal-v2", "version": "0.0.1", "private": true, "type": "module", "nx": {"name": "ui-partner-portal-v2", "projectType": "application", "sourceRoot": "apps/ui-partner-portal-v2/src", "targets": {"ladle:serve": {"executor": "nx:run-commands", "options": {"cwd": "apps/ui-partner-portal-v2", "commands": ["ladle serve"]}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build"], "options": {"file": "./apps/ui-partner-portal-v2/Dockerfile", "platforms": ["linux/arm64"], "build-args": ["BUILD_VERSION=${BUILD_VERSION}"]}, "configurations": {"development": {"load": true, "tags": ["ui-partner-portal-v2:latest"]}, "production": {"cache-from": ["type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-partner-portal-v2:cache"], "cache-to": ["mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-partner-portal-v2:cache"], "metadata": {"images": ["528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-partner-portal-v2"], "tags": ["type=sha,format=short", "type=ref,event=tag"]}, "push": true}}}, "test-watch": {"executor": "nx:run-commands", "options": {"command": "vitest --watch --coverage", "cwd": "apps/ui-partner-portal-v2"}}, "docker": {"executor": "nx:run-commands", "options": {"command": "doppler run -p forward-apps -c dev_ui_partner_portal_v2 -- docker compose --env-file /dev/null -f ./docker-compose.yml up", "cwd": "apps/ui-partner-portal-v2"}, "cache": false}, "docker-cleanup": {"executor": "nx:run-commands", "options": {"command": "docker compose -f ./docker-compose.yml rm -fs", "cwd": "apps/ui-partner-portal-v2"}, "cache": false}, "e2e": {"executor": "nx:run-commands", "options": {"commands": ["HOST_OVERRIDE=qaportal-v2.dev.getfwd.com bun run nx run ui-partner-portal-v2:docker -- -d", "sleep 15", "bun run nx run ui-partner-portal-v2-e2e:playwright || true", "bun run nx run ui-partner-portal-v2:docker-cleanup"], "parallel": false}}, "build": {"cache": true}, "tsc": {"executor": "nx:run-commands", "options": {"cwd": "apps/ui-partner-portal-v2", "command": "tsc --build --incremental"}}}}, "devDependencies": {"@dbd/nextjs-instrumentation": "workspace:*", "@dbd/next-sessions": "workspace:*", "@dbd/tailwind-components": "workspace:*", "@dbd/reporting-merchant-accounts": "workspace:*", "@dbd/reporting-merchant-apps": "workspace:*", "@dbd/reporting-businesses": "workspace:*", "@dbd/reporting-contractors": "workspace:*", "@dbd/reporting-partners": "workspace:*", "@dbd/reporting-auth": "workspace:*", "@dbd/reporting-users": "workspace:*", "@dbd/reporting-payments": "workspace:*", "@dbd/reporting-disputes": "workspace:*", "@dbd/reporting-refunds": "workspace:*", "@dbd/reporting-payouts": "workspace:*", "@dbd/reporting-payout-events": "workspace:*", "@dbd/reporting-accounts": "workspace:*", "@dbd/reporting-common-server": "workspace:*", "@dbd/reporting-transactions": "workspace:*", "@dbd/service-client-library": "workspace:*", "@dbd/flagsmith": "workspace:*", "@dbd/errors": "workspace:*", "@dbd/core-types": "workspace:*", "@dbd/zod-types": "workspace:*", "@dbd/zod-types-common": "workspace:*", "@dbd/ui-providers": "workspace:*", "@dbd/account-service-client": "workspace:*", "@dbd/ui": "workspace:*", "@dbd/ui-data-table": "workspace:*", "@dbd/ui-forms": "workspace:*", "@dbd/ui-accounts": "workspace:*", "@dbd/ui-residuals": "workspace:*", "@dbd/ui-contractors": "workspace:*", "@dbd/ui-users": "workspace:*", "@dbd/ui-partners": "workspace:*", "@dbd/ui-merchants": "workspace:*", "@dbd/ui-merchant-apps": "workspace:*", "@dbd/ui-payouts": "workspace:*", "@dbd/ui-payments": "workspace:*", "@dbd/ui-disputes": "workspace:*", "@dbd/ui-refunds": "workspace:*", "@dbd/ui-businesses": "workspace:*", "@dbd/ui-transactions": "workspace:*", "@dbd/ui-tenants": "workspace:*", "@dbd/ui-charts": "workspace:*"}, "dependencies": {"react": "18.3.1", "@sentry/nextjs": "9.15.0", "lucide-react": "0.419.0", "nuqs": "2.4.1", "@tanstack/react-table": "8.17.3", "sanitize-html": "2.13.0", "react-dom": "18.3.1", "date-fns-tz": "3.1.3", "@faker-js/faker": "9.0.0", "@ts-rest/serverless": "3.52.1", "pino": "9.6.0", "date-fns": "3.6.0", "@vis.gl/react-google-maps": "1.4.2", "flagsmith": "3.24.0", "@testing-library/react": "16.1.0", "react-retool": "1.4.0", "tailwindcss": "3.4.3", "tailwindcss-animate": "1.0.7", "tailwind-scrollbar": "3.1.0", "@testing-library/jest-dom": "6.4.5", "undici": "7.2.3", "@nx/next": "20.6.4", "@nx/react": "20.6.4", "next": "14.2.28", "@ts-rest/react-query": "3.52.1", "@ts-rest/core": "3.52.1", "@vercel/otel": "1.9.1", "@opentelemetry/api": "1.9.0", "@opentelemetry/auto-instrumentations-node": "0.54.0", "@opentelemetry/core": "1.29.0", "@opentelemetry/exporter-logs-otlp-grpc": "0.56.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.56.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.56.0", "@opentelemetry/propagator-b3": "1.29.0", "@opentelemetry/resources": "1.29.0", "@opentelemetry/sdk-logs": "0.56.0", "@opentelemetry/sdk-metrics": "1.29.0", "@opentelemetry/sdk-node": "0.56.0", "@opentelemetry/sdk-trace-node": "1.29.0", "@opentelemetry/semantic-conventions": "1.28.0", "@sentry/node": "9.15.0", "@sentry/opentelemetry": "9.15.0"}}