'use client';

import { getResidualsDashboardUrlAction } from '@dbd/ui-residuals/lib/residuals.actions';
import React, { useEffect, useState } from 'react';
import Retool from 'react-retool';

export const RetoolEmbed = ({ height = '100%' }: { height?: string }) => {
  const [dashboardUrl, setDashboardUrl] = useState({ error: false, embedUrl: '' });
  useEffect(() => {
    (async () => {
      const resp = await getResidualsDashboardUrlAction();
      setDashboardUrl({ error: resp.error ?? false, embedUrl: resp.embedUrl });
    })();
  }, [setDashboardUrl]);

  if (dashboardUrl.error) {
    console.error('Error loading residuals dashboard:', dashboardUrl.error);
    return <p>Error loading residuals dashboard...</p>;
  }

  return <Retool url={dashboardUrl.embedUrl} height={height} />;
};
