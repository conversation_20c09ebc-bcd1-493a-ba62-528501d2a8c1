{"name": "@dbd/payments-core-service", "version": "0.0.1", "private": true, "nx": {"name": "payments-core-service", "projectType": "application", "sourceRoot": "apps/payments-core-service/src", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "dependsOn": ["accounts-db:prisma-generate"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/apps/payments-core-service", "format": ["cjs"], "bundle": true, "main": "apps/payments-core-service/src/main.ts", "tsConfig": "apps/payments-core-service/tsconfig.app.json", "assets": ["apps/payments-core-service/src/assets"], "minify": false, "external": ["@fastify/swagger-ui", "fastify", "pino", "redis", "i<PERSON>is", "@aws-sdk/client-dynamodb"], "sourcesContent": false, "generatePackageJson": false, "generateLockfile": true, "thirdParty": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".cjs"}}}, "configurations": {"development": {}, "production": {}}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build", "fastify-instrumentation:build"], "options": {"file": "./apps/payments-core-service/Dockerfile", "platforms": ["linux/arm64"], "build-args": ["BUILD_VERSION=${BUILD_VERSION}"], "cache-from": ["type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/payments-core-service:cache"], "cache-to": ["mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/payments-core-service:cache"], "metadata": {"images": ["528973710517.dkr.ecr.us-east-1.amazonaws.com/payments-core-service"], "tags": ["type=sha,format=short", "type=ref,event=tag"]}}, "configurations": {"development": {"push": false, "load": true}, "production": {"push": true}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "payments-core-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "payments-core-service:build:development"}, "production": {"buildTarget": "payments-core-service:build:production"}}}, "tsc": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/out-tsc"], "options": {"cwd": "apps/payments-core-service", "command": "tsc --build --incremental --emitDeclarationOnly"}}, "test": {"dependsOn": ["accounts-db:prisma-generate"]}}}, "devDependencies": {"@dbd/accounts-db": "workspace:*", "@dbd/analytics-common": "workspace:*", "@dbd/analytics-fastify": "workspace:*", "@dbd/analytics-types": "workspace:*", "@dbd/bank-gateway-proxy": "workspace:*", "@dbd/cardpointe": "workspace:*", "@dbd/core-db": "workspace:*", "@dbd/core-types": "workspace:*", "@dbd/cross-river-sdk": "workspace:*", "@dbd/fastify": "workspace:*", "@dbd/fee-fastify": "workspace:*", "@dbd/flagsmith": "workspace:*", "@dbd/grailpay-sdk": "workspace:*", "@dbd/payment-gateway-proxy": "workspace:*", "@dbd/plaid-sdk": "workspace:*", "@dbd/service-client-library": "workspace:*", "@dbd/unmatched-settlement-fastify": "workspace:*", "@dbd/unmatched-settlement-types": "workspace:*", "@dbd/ledger-account-fastify": "workspace:*", "@dbd/ledger-account-service-types": "workspace:*", "@dbd/fee-service-types": "workspace:*", "@dbd/fee-types": "workspace:*", "@dbd/utils": "workspace:*", "@dbd/vault-client": "workspace:*", "@dbd/zod-types": "workspace:*", "@dbd/zod-types-common": "workspace:*"}, "dependencies": {"@aws-sdk/client-dynamodb": "3.816.0", "@fastify/rate-limit": "9.1.0", "@fastify/swagger-ui": "3.0.0", "fastify": "4.29.1", "pino": "9.6.0", "redis": "4.6.8", "ioredis": "5.3.2"}}