{"name": "@dbd/terminals", "version": "0.0.1", "private": true, "nx": {"name": "terminals", "projectType": "application", "sourceRoot": "apps/terminals/src", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/apps/terminals", "format": ["cjs"], "bundle": true, "main": "apps/terminals/src/main.ts", "tsConfig": "apps/terminals/tsconfig.app.json", "assets": ["apps/terminals/src/assets"], "external": ["@fastify/swagger-ui", "fastify", "pino", "redis", "i<PERSON>is", "@aws-sdk/client-dynamodb"], "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".cjs"}}, "thirdParty": true, "generateLockfile": true, "generatePackageJson": false}, "configurations": {"development": {}, "production": {}}, "generateLockfile": true, "generatePackageJson": false}, "container": {"dependsOn": ["build", "fastify-instrumentation:build"], "executor": "@nx-tools/nx-container:build", "options": {"cache-from": ["type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/terminals-service:cache"], "cache-to": ["mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/terminals-service:cache"], "file": "./apps/terminals/Dockerfile", "metadata": {"images": ["528973710517.dkr.ecr.us-east-1.amazonaws.com/terminals-service"], "tags": ["type=sha,format=short", "type=ref,event=tag"]}, "platforms": ["linux/arm64"], "build-args": ["BUILD_VERSION=${BUILD_VERSION}"]}, "configurations": {"development": {"push": false}, "production": {"push": true}}}, "docker-build": {"command": "docker build -f apps/terminals/Dockerfile . -t terminals-service", "dependsOn": ["build"]}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "terminals:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "terminals:build:development"}, "production": {"buildTarget": "terminals:build:production"}}}, "tsc": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/out-tsc"], "options": {"cwd": "apps/terminals", "command": "tsc --build --incremental"}}, "test-watch": {"executor": "nx:run-commands", "options": {"command": "vitest --watch --ui --coverage", "cwd": "apps/terminals"}}}}, "devDependencies": {"@dbd/terminals-service-terminals-types": "workspace:*", "@dbd/terminals-service-terminals-fastify": "workspace:*", "@dbd/terminals-service-terminals-common": "workspace:*", "@dbd/core-db": "workspace:*", "@dbd/core-types": "workspace:*", "@dbd/fastify": "workspace:*", "@dbd/errors": "workspace:*", "@dbd/utils": "workspace:*"}, "dependencies": {"@aws-sdk/client-dynamodb": "3.816.0", "@fastify/rate-limit": "9.1.0", "@fastify/swagger-ui": "3.0.0", "fastify": "4.29.1", "pino": "9.6.0", "redis": "4.6.8", "ioredis": "5.3.2"}}