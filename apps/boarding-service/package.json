{"name": "@dbd/boarding-service", "version": "0.0.1", "private": true, "devDependencies": {"@dbd/forward-spec": "workspace:*", "@typespec/compiler": "0.67.1", "@typespec/http": "0.67.1", "@typespec/json-schema": "0.67.1", "@typespec/openapi": "0.67.1", "@typespec/openapi3": "0.67.1", "@typespec/protobuf": "0.67.1", "@typespec/rest": "0.67.1", "@typespec/versioning": "0.67.1", "@dbd/core-types": "workspace:*", "@dbd/errors": "workspace:*", "@dbd/fastify": "workspace:*", "@dbd/flagsmith": "workspace:*", "@dbd/fwdevents": "workspace:*", "@dbd/risk-types": "workspace:*", "@dbd/risk": "workspace:*", "msw": "2.7.3", "kysely": "0.27.5", "@vitest/coverage-v8": "3.0.9", "@vitest/ui": "3.0.9", "vitest": "3.0.9"}, "dependencies": {"@fastify/swagger-ui": "3.0.0", "fastify": "4.29.1", "pino": "9.6.0"}, "nx": {"name": "boarding-service", "projectType": "application", "sourceRoot": "apps/boarding-service/src", "targets": {"build": {"dependsOn": ["gen:openapi:external", "gen:openapi:internal", "gen:events"], "configurations": {"development": {}, "production": {}}, "defaultConfiguration": "production", "executor": "@nx/esbuild:esbuild", "options": {"assets": [{"glob": "**/*.yaml", "input": "{projectRoot}/gen", "output": "gen"}, {"glob": "**/*.json", "input": "{projectRoot}/gen", "output": "gen"}, {"glob": "mockdata.json", "input": "{projectRoot}/src/test", "output": "."}], "bundle": true, "esbuildOptions": {"outExtension": {".js": ".js"}, "sourcemap": true}, "external": ["@fastify/swagger-ui", "fastify", "pino"], "format": ["cjs"], "generatePackageJson": false, "generateLockfile": true, "main": "apps/boarding-service/src/main.ts", "minify": false, "outputPath": "dist/apps/boarding-service", "platform": "node", "sourcesContent": false, "thirdParty": true, "tsConfig": "apps/boarding-service/tsconfig.app.json"}, "outputs": ["{options.outputPath}"]}, "container": {"dependsOn": ["build", "fastify-instrumentation:build"], "executor": "@nx-tools/nx-container:build", "options": {"cache-from": ["type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/boarding-service:cache"], "cache-to": ["mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/boarding-service:cache"], "file": "./apps/boarding-service/Dockerfile", "metadata": {"images": ["528973710517.dkr.ecr.us-east-1.amazonaws.com/boarding-service"], "tags": ["type=sha,format=short", "type=ref,event=tag"]}, "platforms": ["linux/arm64", "linux/amd64"], "build-args": ["BUILD_VERSION=${BUILD_VERSION}"]}, "configurations": {"development": {"push": false}, "production": {"push": true}}}, "dev": {"executor": "@nx/js:node", "options": {"buildTarget": "boarding-service:build"}}, "docker-build": {"command": "docker build -f apps/boarding-service/Dockerfile . -t dbd/boarding-service", "dependsOn": ["build"]}, "gen": {"executor": "nx:run-commands", "inputs": ["{projectRoot}/@typespec"], "options": {"color": true, "commands": ["mkdir -p gen/postman gen/@typespec/json-schema gen/@typespec/openapi3", "bun run nx run boarding-service:gen:openapi:internal", "bun run nx run boarding-service:gen:openapi:external", "bun run nx run boarding-service:gen:events", "bun run nx run boarding-service:gen:postman:internal", "bun run nx run boarding-service:gen:postman:external"], "cwd": "apps/boarding-service", "parallel": false}, "outputs": ["{projectRoot}/gen"]}, "gen:events": {"executor": "nx:run-commands", "options": {"color": true, "parallel": false, "commands": [{"command": "bun run nx run risk:gen:events-boarding"}, {"command": "cp -rf libs/risk/src/modules/boarding-service/events/@typespec/json-schema apps/boarding-service/gen/@typespec"}], "outputs": ["{projectRoot}/gen/@typespec/json-schema"]}}, "gen:openapi:external": {"executor": "nx:run-commands", "options": {"color": true, "command": "tsp compile @typespec/api-external.tsp --output-dir gen --options=@typespec/openapi3.output-file=openapi.external.yaml --emit @typespec/openapi3", "cwd": "apps/boarding-service", "parallel": false}}, "gen:openapi:internal": {"executor": "nx:run-commands", "options": {"color": true, "command": "tsp compile @typespec/api-internal.tsp --output-dir gen --options=@typespec/openapi3.output-file=openapi.internal.yaml --emit @typespec/openapi3", "cwd": "apps/boarding-service", "parallel": false}}, "gen:postman:external": {"executor": "nx:run-commands", "options": {"color": true, "command": "openapi2postmanv2 -s gen/@typespec/openapi3/openapi.external.yaml -o gen/postman/boarding.external.collection.json", "cwd": "apps/boarding-service", "parallel": false}}, "gen:postman:internal": {"executor": "nx:run-commands", "options": {"color": true, "command": "openapi2postmanv2 -s gen/@typespec/openapi3/openapi.internal.yaml -o gen/postman/boarding.internal.collection.json", "cwd": "apps/boarding-service", "parallel": false}}, "serve-docker-deps": {"command": "docker compose -p boarding-stack -f apps/boarding-service/config/docker-compose.deps.yaml up --remove-orphans"}, "serve-docker": {"command": "docker compose -p boarding-stack -f apps/boarding-service/config/docker-compose.yaml -f apps/boarding-service/config/docker-compose.deps.yaml up --remove-orphans", "dependsOn": ["build", "docker-build"]}, "tsc": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/out-tsc"], "options": {"cwd": "apps/boarding-service", "command": "tsc --build --incremental --emitDeclarationOnly"}}, "wiretap": {"dependsOn": ["gen:openapi:external"], "executor": "nx:run-commands", "options": {"cwd": "apps/boarding-service", "command": "wiretap"}}, "wiretap-internal": {"dependsOn": ["gen:openapi:internal"], "executor": "nx:run-commands", "options": {"cwd": "apps/boarding-service", "command": "wiretap -c wiretap.internal.yaml"}}}}}