import { Logger, LogWithRequestTracing } from '@dbd/serverless-logger';
import { DBDClient } from '@dbd/service-client-library';
import * as Sentry from '@sentry/aws-serverless';
import { Context, SNSEvent } from 'aws-lambda';

import { toPayeeApplication } from './payee-submitted.mapper';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.AWS_ENV,
  tracesSampleRate: 1.0,
  release: process.env.BUILD_VERSION,
});

const forwardClient = new DBDClient();

export const main = async (event: SNSEvent, context: Context) => {
  LogWithRequestTracing(event, context);
  try {
    return Promise.all(
      event.Records.map(async (message) => {
        const record = JSON.parse(message.Sns.Message);
        if (!Object.entries(record).length) {
          throw new Error('empty object cannot be parsed');
        }
        const { data, error } = await toPayeeApplication(record, forwardClient);
        if (error) {
          Logger.error(error);
          return;
        }

        await forwardClient.boardings.submitPayeeToTaktile(data);
      }),
    );
  } catch (reason: unknown) {
    Logger.error(reason);
    const err = reason instanceof Error ? reason : new Error(reason as string);
    Sentry.captureException(err.message);
    throw err;
  }
};
