import { DBDClient } from '@dbd/service-client-library';
import { describe, expect, it, vi } from 'vitest';

import { CompanyFiling } from './mock/company.js';
import { IndividualFiling } from './mock/individual.js';
import { toPayeeApplication } from './payee-submitted.mapper.js';

// Mock DBDClient
vi.mock('@dbd/service-client-library');

const mockDBDClient = {
  encryption: {
    decrypt: vi.fn().mockResolvedValue({ decrypted_data: '*********' }),
  },
  hierarchy: {
    retrieve: vi.fn().mockResolvedValue({
      business: { id: 'bus_1234', name: 'Test Business' },
      partner: { id: 'part_1234', name: 'Test Partner' },
      tenant: { id: 'tenant_1234', name: 'Test Tenant' },
    }),
  },
} as unknown as DBDClient;

describe('Payee Submitted Mapper', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('toPayeeApplication', () => {
    it('should successfully process valid individual data', async () => {
      const result = await toPayeeApplication(IndividualFiling, mockDBDClient);

      if (!result.success) {
        console.log('Error:', result.error);
      }
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
    });

    it('should successfully process valid company data', async () => {
      const result = await toPayeeApplication(CompanyFiling, mockDBDClient);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
    });

    it('should handle invalid data gracefully', async () => {
      const invalidData = { invalid: 'data' };
      const result = await toPayeeApplication(invalidData, mockDBDClient);

      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
    });

    it('should handle empty data gracefully', async () => {
      const result = await toPayeeApplication({}, mockDBDClient);

      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
    });
  });

  describe('Field Transformation Logic', () => {
    describe('Individual Applications', () => {
      it('should transform first_name + last_name to legal_name and name', async () => {
        const individualData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              individual: {
                ...IndividualFiling.data.object.individual,
                first_name: 'John',
                last_name: 'Doe',
                // Ensure required fields are present
                email: '<EMAIL>',
                application_id: 'test_app_123',
                parent_type: 'PARTNER',
                plaid_manual_verification: false,
                address: {
                  city: 'Test City',
                  state: 'TS',
                  country: 'US',
                  zip_code: '12345',
                  address_line1: '123 Test St',
                  address_line2: '',
                },
              },
            },
          },
        };

        const result = await toPayeeApplication(individualData, mockDBDClient);

        if (!result.success) {
          console.log('Transform test error:', result.error);
        }
        expect(result.success).toBe(true);
        expect(result.data?.individual?.legal_name).toBe('John Doe');
        expect(result.data?.individual?.name).toBe('John Doe');
      });

      it('should handle missing first_name gracefully', async () => {
        const individualData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              individual: {
                ...IndividualFiling.data.object.individual,
                first_name: undefined,
                last_name: 'Doe',
              },
            },
          },
        };

        const result = await toPayeeApplication(individualData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.individual?.legal_name).toBe('Doe');
        expect(result.data?.individual?.name).toBe('Doe');
      });

      it('should handle missing last_name gracefully', async () => {
        const individualData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              individual: {
                ...IndividualFiling.data.object.individual,
                first_name: 'John',
                last_name: undefined,
              },
            },
          },
        };

        const result = await toPayeeApplication(individualData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.individual?.legal_name).toBe('John');
        expect(result.data?.individual?.name).toBe('John');
      });

      it('should handle missing both names gracefully', async () => {
        const individualData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              individual: {
                ...IndividualFiling.data.object.individual,
                first_name: undefined,
                last_name: undefined,
              },
            },
          },
        };

        const result = await toPayeeApplication(individualData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.individual?.legal_name).toBe('');
        expect(result.data?.individual?.name).toBe('');
      });

      it('should handle null plaid_public_token for individuals', async () => {
        const individualData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              individual: {
                ...IndividualFiling.data.object.individual,
                plaid_public_token: null,
              },
            },
          },
        };

        const result = await toPayeeApplication(individualData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.individual?.plaid_public_token).toBeNull();
      });

      it('should handle undefined plaid_public_token for individuals', async () => {
        const individualData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              individual: {
                ...IndividualFiling.data.object.individual,
                plaid_public_token: undefined,
              },
            },
          },
        };

        const result = await toPayeeApplication(individualData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.individual?.plaid_public_token).toBeUndefined();
      });
    });

    describe('Company Applications', () => {
      it('should use legal_name as name when name is missing', async () => {
        const companyData = {
          ...CompanyFiling,
          data: {
            ...CompanyFiling.data,
            object: {
              ...CompanyFiling.data.object,
              company: {
                ...CompanyFiling.data.object.company,
                name: undefined,
                legal_name: 'Test Company LLC',
              },
            },
          },
        };

        const result = await toPayeeApplication(companyData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.company?.name).toBe('Test Company LLC');
        expect(result.data?.company?.legal_name).toBe('Test Company LLC');
      });

      it('should use name as legal_name when legal_name is missing', async () => {
        const companyData = {
          ...CompanyFiling,
          data: {
            ...CompanyFiling.data,
            object: {
              ...CompanyFiling.data.object,
              company: {
                ...CompanyFiling.data.object.company,
                name: 'Test Company Inc',
                legal_name: undefined,
              },
            },
          },
        };

        const result = await toPayeeApplication(companyData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.company?.name).toBe('Test Company Inc');
        expect(result.data?.company?.legal_name).toBe('Test Company Inc');
      });

      it('should handle missing both name and legal_name gracefully', async () => {
        const companyData = {
          ...CompanyFiling,
          data: {
            ...CompanyFiling.data,
            object: {
              ...CompanyFiling.data.object,
              company: {
                ...CompanyFiling.data.object.company,
                name: undefined,
                legal_name: undefined,
              },
            },
          },
        };

        const result = await toPayeeApplication(companyData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.company?.name).toBe('');
        expect(result.data?.company?.legal_name).toBe('');
      });

      it('should handle null plaid_public_token for companies', async () => {
        const companyData = {
          ...CompanyFiling,
          data: {
            ...CompanyFiling.data,
            object: {
              ...CompanyFiling.data.object,
              company: {
                ...CompanyFiling.data.object.company,
                plaid_public_token: null,
              },
            },
          },
        };

        const result = await toPayeeApplication(companyData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.company?.plaid_public_token).toBeNull();
      });

      it('should handle undefined plaid_public_token for companies', async () => {
        const companyData = {
          ...CompanyFiling,
          data: {
            ...CompanyFiling.data,
            object: {
              ...CompanyFiling.data.object,
              company: {
                ...CompanyFiling.data.object.company,
                plaid_public_token: undefined,
              },
            },
          },
        };

        const result = await toPayeeApplication(companyData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.company?.plaid_public_token).toBeUndefined();
      });
    });

    describe('Edge Cases', () => {
      it('should handle data with neither company nor individual', async () => {
        const invalidData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              company: null,
              individual: null,
            },
          },
        };

        const result = await toPayeeApplication(invalidData, mockDBDClient);

        expect(result.success).toBe(false);
        expect(result.data).toBeNull();
        expect(result.error).toBeDefined();
        expect(result.error?.message).toContain('Neither company nor individual data found');
      });

      it('should handle whitespace-only names for individuals', async () => {
        const individualData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              individual: {
                ...IndividualFiling.data.object.individual,
                first_name: '   ',
                last_name: '   ',
              },
            },
          },
        };

        const result = await toPayeeApplication(individualData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.individual?.legal_name).toBe('');
        expect(result.data?.individual?.name).toBe('');
      });
    });
  });
});
