import { DBDClient } from '@dbd/service-client-library';
import { describe, expect, it, vi } from 'vitest';

import { CompanyFiling } from './mock/company.js';
import { toPayeeApplication } from './payee-submitted.mapper.js';

// Mock DBDClient
vi.mock('@dbd/service-client-library');

const mockDBDClient = {
  encryption: { decrypt: vi.fn().mockResolvedValue({ decrypted_data: '*********' }) },
  hierarchy: {
    retrieve: vi.fn().mockResolvedValue({
      business: { id: 'bus_1234', name: 'Test Business' },
      partner: { id: 'part_1234', name: 'Test Partner' },
      tenant: { id: 'tenant_1234', name: 'Test Tenant' },
    }),
  },
} as unknown as DBDClient;

describe('Payee Submitted Mapper', () => {
  it('should process valid company data', async () => {
    const result = await toPayeeApplication(CompanyFiling, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.company).toBeDefined();
  });

  it('should handle null plaid_public_token', async () => {
    const dataWithNullToken = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: {
          ...CompanyFiling.data.object,
          company: { ...CompanyFiling.data.object.company, plaid_public_token: null },
        },
      },
    };

    const result = await toPayeeApplication(dataWithNullToken, mockDBDClient);
    expect(result.success).toBe(true);
    expect(result.data?.company?.plaid_public_token).toBeNull();
  });

  it('should handle invalid data', async () => {
    const result = await toPayeeApplication({ invalid: 'data' }, mockDBDClient);
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });

  it('should handle missing company and individual data', async () => {
    const dataWithoutEntity = {
      ...CompanyFiling,
      data: {
        ...CompanyFiling.data,
        object: { ...CompanyFiling.data.object, company: null, individual: null },
      },
    };

    const result = await toPayeeApplication(dataWithoutEntity, mockDBDClient);
    expect(result.success).toBe(false);
    expect(result.error?.message).toContain('Neither company nor individual data found');
  });
});
