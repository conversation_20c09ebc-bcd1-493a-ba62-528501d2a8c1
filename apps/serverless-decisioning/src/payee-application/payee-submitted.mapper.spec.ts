import { DBDClient } from '@dbd/service-client-library';
import { describe, expect, it, vi } from 'vitest';

import { CompanyFiling } from './mock/company.js';
import { IndividualFiling } from './mock/individual.js';
import { toPayeeApplication } from './payee-submitted.mapper.js';

// Mock DBDClient
vi.mock('@dbd/service-client-library');

const mockDBDClient = {
  encryption: {
    decrypt: vi.fn().mockResolvedValue({ decrypted_data: '*********' }),
  },
  hierarchy: {
    retrieve: vi.fn().mockResolvedValue({
      business: { id: 'bus_1234', name: 'Test Business' },
      partner: { id: 'part_1234', name: 'Test Partner' },
      tenant: { id: 'tenant_1234', name: 'Test Tenant' },
    }),
  },
} as unknown as DBDClient;

describe('Payee Submitted Mapper', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('toPayeeApplication', () => {
    it('should successfully process valid company data', async () => {
      const result = await toPayeeApplication(CompanyFiling, mockDBDClient);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
    });

    it('should handle invalid data gracefully', async () => {
      const invalidData = { invalid: 'data' };
      const result = await toPayeeApplication(invalidData, mockDBDClient);

      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
    });

    it('should handle empty data gracefully', async () => {
      const result = await toPayeeApplication({}, mockDBDClient);

      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
    });
  });

  describe('Field Transformation Logic', () => {
    describe('Company Applications', () => {
      it('should successfully process company data', async () => {
        const result = await toPayeeApplication(CompanyFiling, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.company).toBeDefined();
        expect(result.data?.company?.name).toBeDefined();
        expect(result.data?.company?.legal_name).toBeDefined();
      });

      it('should handle null plaid_public_token for companies', async () => {
        const companyData = {
          ...CompanyFiling,
          data: {
            ...CompanyFiling.data,
            object: {
              ...CompanyFiling.data.object,
              company: {
                ...CompanyFiling.data.object.company,
                plaid_public_token: null,
              },
            },
          },
        };

        const result = await toPayeeApplication(companyData, mockDBDClient);

        expect(result.success).toBe(true);
        expect(result.data?.company?.plaid_public_token).toBeNull();
      });
    });

    describe('Edge Cases', () => {
      it('should handle data with neither company nor individual', async () => {
        const invalidData = {
          ...IndividualFiling,
          data: {
            ...IndividualFiling.data,
            object: {
              ...IndividualFiling.data.object,
              company: null,
              individual: null,
            },
          },
        };

        const result = await toPayeeApplication(invalidData, mockDBDClient);

        expect(result.success).toBe(false);
        expect(result.data).toBeNull();
        expect(result.error).toBeDefined();
        expect(result.error?.message).toContain('Neither company nor individual data found');
      });
    });
  });
});
