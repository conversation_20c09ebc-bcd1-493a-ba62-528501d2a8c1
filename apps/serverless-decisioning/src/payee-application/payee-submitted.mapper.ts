import { HierarchyTypeEnum } from '@dbd/core-types/enums/common';
import { PayeeApplicationSubmittedEvent, PayeeCompany, PayeeIndividual, PayeeOnboardingInput } from '@dbd/risk-types';
import { type DBDClient } from '@dbd/service-client-library';
import { Simplify } from 'type-fest';
import { z } from 'zod';

const WrappedPayeeApplicationSubmittedEvent = z.object({
  subject: z.string(),
  data: z.object({
    meta: z.object({
      auth: z.object({
        context: z.object({
          tenant_id: z.string().nullish(),
          account_id: z.string().nullish(),
          partner_id: z.string().nullish(),
          business_id: z.string().nullish(),
          enterprise_id: z.string().nullish(),
        }),
      }),
    }),
    object: z.object({
      company: z.any().nullable(), // More lenient - we'll validate after transformation
      individual: z.any().nullable(), // More lenient - we'll validate after transformation
      entity_data: z.object({
        created_at: z.string().nullish(),
        type: z.enum(['COMPANY', 'INDIVIDUAL']).optional(), // Make optional since it might be missing
      }),
    }),
  }),
});

type WrappedPayeeApplicationSubmittedEvent = Simplify<z.infer<typeof WrappedPayeeApplicationSubmittedEvent>>;

/**
 *
 * @param eventData - Possibly Payee Application data
 * @returns
 */
export async function toPayeeApplication(eventData: unknown, dbdClient: DBDClient) {
  const { data, success, error } = WrappedPayeeApplicationSubmittedEvent.safeParse(eventData);
  if (!success) {
    return { data: null, error, success: false };
  }

  return mapPayload(data, dbdClient);
}

async function mapPayload(eventData: WrappedPayeeApplicationSubmittedEvent, dbdClient: DBDClient) {
  if (isIndividualBoarding(eventData.data.object)) {
    if (eventData.data.object.individual?.ssn_encrypted) {
      eventData.data.object.individual.ssn = (
        await dbdClient.encryption.decrypt(eventData.data.object.individual.ssn_encrypted)
      ).decrypted_data;
    }
  }

  if (isCompanyBoarding(eventData.data.object)) {
    const company = eventData.data.object.company as any;
    if (company?.encrypted_tax_id) {
      company.ein = (await dbdClient.encryption.decrypt(company.encrypted_tax_id as string)).decrypted_data;
    }

    if (company?.owners && Array.isArray(company.owners)) {
      company.owners = await Promise.all(
        company.owners.map(async (owner: NonNullable<PayeeCompany['owners']>[number]) => ({
          ...owner,
          ssn: owner.ssn_encrypted
            ? (await dbdClient.encryption.decrypt(owner.ssn_encrypted ?? undefined)).decrypted_data
            : undefined,
          ownership_percent: owner.ownership_percent ?? undefined, // Convert null to undefined
        })),
      );
    }
  }

  // Transform the data to ensure required fields are present
  const rawData = eventData.data.object.company ?? eventData.data.object.individual;
  if (!rawData) {
    return { data: null, error: new Error('Neither company nor individual data found'), success: false };
  }

  let transformedData: PayeeCompany | PayeeIndividual;
  if (isCompanyBoarding(eventData.data.object)) {
    const companyData = rawData as PayeeCompany;
    transformedData = {
      ...companyData,
      name: companyData.name || companyData.legal_name || '',
      legal_name: companyData.legal_name || companyData.name || '',
    };
  } else {
    const individualData = rawData as PayeeIndividual;
    const fullName = `${individualData.first_name || ''} ${individualData.last_name || ''}`.trim();
    transformedData = {
      ...individualData,
      legal_name: fullName || '',
      name: fullName || '',
    };
  }

  const { data, error, success } = PayeeApplicationSubmittedEvent.safeParse(transformedData);

  if (!success) {
    return { data: null, error, success: false };
  }

  const hierarchy = {
    tenant_id: '',
    tenant_name: '',
    partner_id: '',
    partner_name: '',
    business_name: '',
    business_id: '',
  };

  if (data.parent_type === 'PARTNER' && eventData.data.meta.auth.context.partner_id) {
    const res = await dbdClient.hierarchy.retrieve(
      eventData.data.meta.auth.context.partner_id,
      HierarchyTypeEnum.PARTNER,
    );
    hierarchy.partner_id = res.partner.id;
    hierarchy.partner_name = res.partner.name;

    hierarchy.tenant_id = res.tenant.id;
    hierarchy.tenant_name = res.tenant.name;
  } else if (data.parent_type === 'BUSINESS' && eventData.data.meta.auth.context.business_id) {
    const res = await dbdClient.hierarchy.retrieve(
      eventData.data.meta.auth.context.business_id,
      HierarchyTypeEnum.BUSINESS,
    );

    hierarchy.business_id = res.business.id;
    hierarchy.business_name = res.business.name;

    hierarchy.partner_id = res.partner.id;
    hierarchy.partner_name = res.partner.name;

    hierarchy.tenant_id = res.tenant.id;
    hierarchy.tenant_name = res.tenant.name;
  }

  const input: Partial<PayeeOnboardingInput> = {
    application_id: data.application_id as string,
    ...hierarchy,
    payee_level: data.parent_type as 'PARTNER' | 'BUSINESS',
    payee_id: data.parent_id as string,
    submitted_at: eventData.data.object.entity_data.created_at ?? new Date().toISOString(),
    comments: '',
    ip_address: '',
    company: isCompanyBoarding(eventData.data.object) ? (data as PayeeCompany) : undefined,
    individual: isIndividualBoarding(eventData.data.object) ? (data as PayeeIndividual) : undefined,
  };
  return PayeeOnboardingInput.safeParse(input);
}

function isCompanyBoarding(value: Record<string, any>): value is PayeeCompany {
  return value.company !== null;
}

function isIndividualBoarding(value: Record<string, any>): value is PayeeIndividual {
  return value.individual !== null;
}
