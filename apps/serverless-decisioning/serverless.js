// @ts-check
/**
 * @typedef {import('@dbd/serverless-environments').Environment} Environment
 */

// This file has to be JS because of the serverless limitations when it comes to TS support and serverless config.

const {
  baseServerlessConfigProvider,
  baseServerlessConfiguration,
  getOTELEnv,
} = require('@dbd/serverless-environments/base');
const { environments } = require('@dbd/serverless-environments');

/** @type {import('@dbd/serverless-environments').Environment} */
const env = environments[/** @type {keyof typeof environments} */ (process.env.AWS_ENV ?? 'development')];

/** @type {{ [key: string]: any }} */
const baseEnvironment = /** @type {{ [key: string]: any }} */ (
  baseServerlessConfiguration.provider && baseServerlessConfiguration.provider.environment
    ? baseServerlessConfiguration.provider.environment
    : {}
);

module.exports = (async () => {
  const { ...providerAll } = baseServerlessConfiguration.provider && baseServerlessConfiguration.provider;
  const serverlessConfig = {
    ...baseServerlessConfiguration,
    service: 'serverless-decisioning',
    package: {
      individually: true,
      include: ['collector.yml'],
    },
    provider: {
      ...providerAll,
      memorySize: 512,
      environment: {
        ...baseEnvironment,
        ...getOTELEnv(`serverless-decisioning-${env.name}`),
        ACCOUNTS_SERVICE_URL: env.accountServiceUrl,
        BOARDING_SERVICE_URL: env.boardingServiceUrl,
        CLIENT_ID: env.clientId,
        CLIENT_SECRET: env.clientSecret,
        DECISIONING_SERVICE_URL: env.decisioningServiceUrl,
        FLAGSMITH_API_URL: env.flagsmith.apiUrl,
        FLAGSMITH_ENVIRONMENT_ID: env.flagsmith.environmentId,
        FUSIONAUTH_URL: env.fusionAuthUrl,
        MICROSERVICE_MASTER_ENTITY_ID: env.microserviceMasterEntityId,
        SENTRY_DSN: 'https://<EMAIL>/****************',
      },
      apiGateway: {
        apiKeys: [
          // TODO: Delete inactive key later
          { name: 'taktile_webhook_secret', value: '${env:TAKTILE_WEBHOOK_SECRET, "default"}' },
          // active keys below
          //
          // API calls from workflows use this key!
          { name: 'taktile_api_key', value: '${env:TAKTILE_API_KEY, "default"}' },
          // Workflow webhooks use this key!
          { name: 'taktile_webhook_ingest_key', value: '${env:TAKTILE_WEBHOOK_INGEST_KEY, "default"}' },
        ],
      },
      iam: {
        ...baseServerlessConfigProvider.iam,
        role: {
          statements: [
            {
              Effect: 'Allow',
              Action: ['dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:PutItem', 'dynamodb:UpdateItem'],
              Resource: '*',
            },
            {
              Effect: 'Allow',
              Action: ['sqs:SendMessage', 'sqs:ReceiveMessage', 'sqs:DeleteMessage', 'secretsmanager:GetSecretValue'],
              Resource: '*',
            },
            {
              Effect: 'Allow',
              Action: ['sns:Publish'],
              Resource: '*',
            },
            {
              Effect: 'Allow',
              Action: ['lambda:InvokeFunction', 'lambda:InvokeAsync'],
              Resource: '*',
            },
            {
              Effect: 'Allow',
              Action: ['s3:DeleteObject', 's3:GetObject', 's3:PutObject', 's3:PutObjectAcl', 's3:CreateBucket'],
              Resource: '*',
            },
            {
              Effect: 'Allow',
              Action: ['states:ListExecutions', 'states:SendTaskSuccess', 'states:StartExecution'],
              Resource: '*',
            },
          ],
          managedPolicies: ['arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess'],
        },
      },
      layers: baseServerlessConfiguration?.provider?.layers ?? [],
      vpc: env.vpc,
    },
    custom: {
      ...baseServerlessConfiguration.custom,
      // use the same host as core for decisioning messages
      customDomains: env.customDomains.decisioning,
      'serverless-offline': {
        lambdaPort: 3002,
        httpPort: 3003,
      },
      esbuild: {
        config: './esbuild.config.cjs',
      },
      warmup: { default: { enabled: false } },
      datadog: {},
    },
    functions: {
      disablePayouts: {
        handler: 'src/handlers/disable-payouts-handler.handler',
        description: 'disable payouts',
        warmup: { default: { enabled: true } },
        events: [
          {
            http: {
              method: 'post',
              path: 'taktile/disable_payouts', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      disableProcessing: {
        handler: 'src/handlers/disable-processing-handler.handler',
        description: 'disable processing',
        warmup: { default: { enabled: true } },
        events: [
          {
            http: {
              method: 'post',
              path: 'taktile/disable_processing', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      taktileDecryptMessage: {
        handler: 'src/handlers/taktile-decrypt-handler.handler',
        description: 'Taktile decrypt message route',
        events: [
          {
            http: {
              method: 'post',
              path: 'taktile/decrypt', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      taktileIngestWebhooks: {
        handler: 'src/handlers/taktile-ingest-webhooks-handler.handler',
        description: 'Taktile webhook ingestion route',
        warmup: { default: { enabled: true } },
        events: [
          {
            http: {
              method: 'post',
              path: 'taktile/inbound/webhook', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      taktileSubmitHandler: {
        handler: 'src/handlers/taktile-submit-handler.handler',
        description: 'record Taktile dispositions for merchant and payee application and monitoring activities',
        timeout: 30,
        events: [
          {
            sqs: {
              arn: env.sqs && env.sqs.taktileQueueArn,
              batchSize: 1,
            },
          },
        ],
      },
      merchantApplication: {
        handler: 'src/handlers/merchant-application-handler.handler',
        description: 'submit merchant boarding application',
        events: [
          {
            sns: env.sns.applicationStatuses.submitted,
          },
        ],
      },
      payeeApplication: {
        handler: 'src/handlers/payee-application-handler.handler',
        description: 'submit payee (contractor) application',
        events: [
          {
            sns: env.sns.applicationStatuses.payeeApplicationUnderReview,
          },
        ],
      },
      riskProfileAccountRisk: {
        handler: 'src/handlers/account-risk-handler.handler',
        description: 'account risk profile',
        warmup: { default: { enabled: true } },
        events: [
          {
            http: {
              method: 'post',
              path: 'taktile/risk_profile/merchant_account', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      riskProfileAccountLimit: {
        handler: 'src/handlers/account-limit-handler.handler',
        description: 'account limit risk profile',
        warmup: { default: { enabled: true } },
        events: [
          {
            http: {
              method: 'post',
              path: 'taktile/risk_profile/merchant_limit', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      riskProfileCreditRisk: {
        handler: 'src/handlers/credit-risk-handler.handler',
        description: 'credit risk profile',
        warmup: { default: { enabled: true } },
        events: [
          {
            http: {
              method: 'post',
              path: 'taktile/risk_profile/credit_risk', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      ruleViolation: {
        handler: 'src/handlers/rule-violations-handler.handler',
        description: 'retrieve rule violations for a merchant',
        events: [
          {
            http: {
              method: 'get',
              request: {
                parameters: {
                  paths: {
                    rule_violator_account_id: true,
                  },
                },
              },
              path: 'taktile/rule_violations/{rule_violator_account_id}', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      applicationHighlights: {
        handler: 'src/handlers/application-highlights-handler.handler',
        description: 'retrieve application highlights for a merchant',
        events: [
          {
            http: {
              method: 'get',
              request: {
                parameters: {
                  paths: {
                    application_id: true,
                  },
                },
              },
              path: 'taktile/application_highlights/{application_id}', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      boardingErrors: {
        handler: 'src/handlers/boarding-errors-notifier.handler',
        description: 'errors related to merchant boarding',
        environment: {
          SLACK_BOARDING_ERROR_WEBHOOK_URL: env.decisioning.slack.boardingErrorWebhookUrl,
        },
        events: [
          {
            sns: env.decisioning.boarding.snsErrors,
          },
        ],
      },
      fetchMerchantAccountRisk: {
        handler: 'src/handlers/fetch-merchant-account-risk-handler.handler',
        description: 'retrieve account risk for a merchant',
        events: [
          {
            http: {
              method: 'get',
              request: {
                parameters: {
                  paths: {
                    merchant_account_id: true,
                  },
                },
              },
              path: 'taktile/risk_profile/merchant_account/merchants/{merchant_account_id}', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      fetchCreditRisk: {
        handler: 'src/handlers/fetch-credit-risk-handler.handler',
        description: 'retrieve credit risk for a merchant',
        events: [
          {
            http: {
              method: 'get',
              request: {
                parameters: {
                  paths: {
                    merchant_id: true,
                  },
                },
              },
              path: 'taktile/risk_profile/credit_risk/merchants/{merchant_id}', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
      jobDailiyMonitoring: {
        handler: 'src/handlers/job-daily-monitoring-handler.handler',
        description: 'retrieve the daily monitoring job run data webhook',
        environment: {
          SLACK_DAILIY_MONITORING_WEBHOOK_URL: env.decisioning.slack.dailyMonitoringWebhookUrl,
        },
        events: [
          {
            http: {
              method: 'post',
              path: 'taktile/job/txn-monitoring/webhook', // This creates a proxy endpoint
              private: true,
            },
          },
        ],
      },
    },
    resources: {
      Resources: {},
    },
  };
  return serverlessConfig;
})();
