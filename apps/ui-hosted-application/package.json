{"name": "@dbd/ui-hosted-application", "version": "0.0.1", "private": true, "type": "module", "nx": {"name": "ui-hosted-application", "projectType": "application", "sourceRoot": "apps/ui-hosted-application/src", "targets": {"build": {"cache": false}, "tsc": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/out-tsc"], "options": {"cwd": "apps/ui-hosted-application", "command": "tsc --build --incremental --emitDeclarationOnly"}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build"], "options": {"file": "./apps/ui-hosted-application/Dockerfile", "platforms": ["linux/arm64"], "build-args": ["BUILD_VERSION=${BUILD_VERSION}"]}, "configurations": {"development": {"load": true, "tags": ["ui-hosted-application:latest"]}, "production": {"cache-from": ["type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-hosted-application:cache"], "cache-to": ["mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-hosted-application:cache"], "metadata": {"images": ["528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-hosted-application"], "tags": ["type=sha,format=short", "type=ref,event=tag"]}, "push": true}}}}}, "devDependencies": {"@dbd/nextjs-instrumentation": "workspace:*", "@dbd/core-types": "workspace:*", "@dbd/ui-utils": "workspace:*", "@dbd/ui-hooks": "workspace:*", "@dbd/ui-services": "workspace:*", "@dbd/ui-components": "workspace:*", "@dbd/ui-types": "workspace:*", "@dbd/ui-styles": "workspace:*", "@dbd/ui-providers": "workspace:*", "@dbd/ui-pages": "workspace:*", "@dbd/service-client-library": "workspace:*", "@dbd/utils": "workspace:*"}, "dependencies": {"next": "14.2.28", "react": "18.3.1", "react-dom": "18.3.1"}}