{"name": "@dbd/ui-merchant-portal-v2", "version": "0.0.1", "private": true, "type": "module", "nx": {"name": "ui-merchant-portal-v2", "projectType": "application", "sourceRoot": "apps/ui-merchant-portal-v2/src", "targets": {"ladle:serve": {"executor": "nx:run-commands", "options": {"cwd": "apps/ui-merchant-portal-v2", "commands": ["ladle serve"]}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build"], "options": {"file": "./apps/ui-merchant-portal-v2/Dockerfile", "platforms": ["linux/arm64"], "build-args": ["BUILD_VERSION=${BUILD_VERSION}"]}, "configurations": {"development": {"load": true, "tags": ["ui-merchant-portal-v2:latest"]}, "production": {"cache-from": ["type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-merchant-portal-v2:cache"], "cache-to": ["mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref=528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-merchant-portal-v2:cache"], "metadata": {"images": ["528973710517.dkr.ecr.us-east-1.amazonaws.com/ui-merchant-portal-v2"], "tags": ["type=sha,format=short", "type=ref,event=tag"]}, "push": true}}}, "test-watch": {"executor": "nx:run-commands", "options": {"command": "vitest --watch --coverage", "cwd": "apps/ui-merchant-portal-v2"}}, "build": {"cache": false}, "tsc": {"executor": "nx:run-commands", "options": {"cwd": "apps/ui-merchant-portal-v2", "command": "tsc --build --incremental"}}}}, "devDependencies": {"@dbd/nextjs-instrumentation": "workspace:*", "@dbd/next-sessions": "workspace:*", "@dbd/tailwind-components": "workspace:*", "@dbd/reporting-merchant-accounts": "workspace:*", "@dbd/reporting-merchant-apps": "workspace:*", "@dbd/reporting-businesses": "workspace:*", "@dbd/reporting-contractors": "workspace:*", "@dbd/reporting-partners": "workspace:*", "@dbd/reporting-auth": "workspace:*", "@dbd/reporting-users": "workspace:*", "@dbd/reporting-payments": "workspace:*", "@dbd/reporting-disputes": "workspace:*", "@dbd/reporting-refunds": "workspace:*", "@dbd/reporting-payouts": "workspace:*", "@dbd/reporting-payout-events": "workspace:*", "@dbd/reporting-accounts": "workspace:*", "@dbd/reporting-common-server": "workspace:*", "@dbd/reporting-transactions": "workspace:*", "@dbd/service-client-library": "workspace:*", "@dbd/flagsmith": "workspace:*", "@dbd/errors": "workspace:*", "@dbd/core-types": "workspace:*", "@dbd/zod-types": "workspace:*", "@dbd/zod-types-common": "workspace:*", "@dbd/ui-providers": "workspace:*", "@dbd/account-service-client": "workspace:*", "@dbd/ui": "workspace:*", "@dbd/ui-data-table": "workspace:*", "@dbd/ui-forms": "workspace:*", "@dbd/ui-accounts": "workspace:*", "@dbd/ui-residuals": "workspace:*", "@dbd/ui-contractors": "workspace:*", "@dbd/ui-users": "workspace:*", "@dbd/ui-merchants": "workspace:*", "@dbd/ui-partners": "workspace:*", "@dbd/ui-merchant-apps": "workspace:*", "@dbd/ui-payouts": "workspace:*", "@dbd/ui-payments": "workspace:*", "@dbd/ui-disputes": "workspace:*", "@dbd/ui-refunds": "workspace:*", "@dbd/ui-businesses": "workspace:*", "@dbd/ui-transactions": "workspace:*", "@dbd/ui-tenants": "workspace:*", "@dbd/ui-charts": "workspace:*", "vitest": "3.1.1"}, "dependencies": {"@nx/next": "20.6.4", "next": "14.2.28", "@sentry/nextjs": "9.15.0", "nuqs": "2.4.1", "react": "18.3.1", "lucide-react": "0.419.0", "@tanstack/react-table": "8.17.3", "sanitize-html": "2.13.0", "recharts": "2.13.0", "date-fns-tz": "3.1.3", "@ts-rest/serverless": "3.52.1", "pino": "9.6.0", "flagsmith": "3.24.0", "@hookform/resolvers": "3.9.1", "zod": "3.24.2", "@vis.gl/react-google-maps": "1.4.2", "tailwindcss": "3.4.3", "@nx/react": "20.6.4", "tailwindcss-animate": "1.0.7", "tailwind-scrollbar": "3.1.0", "@testing-library/jest-dom": "6.4.5", "undici": "7.2.3", "@ts-rest/core": "3.52.1", "@heroicons/react": "2.1.3", "@radix-ui/react-icons": "1.3.0", "react-hook-form": "7.41.5", "@vercel/otel": "1.9.1", "@opentelemetry/api": "1.9.0", "@opentelemetry/auto-instrumentations-node": "0.54.0", "@opentelemetry/core": "1.29.0", "@opentelemetry/exporter-logs-otlp-grpc": "0.56.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.56.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.56.0", "@opentelemetry/propagator-b3": "1.29.0", "@opentelemetry/resources": "1.29.0", "@opentelemetry/sdk-logs": "0.56.0", "@opentelemetry/sdk-metrics": "1.29.0", "@opentelemetry/sdk-node": "0.56.0", "@opentelemetry/sdk-trace-node": "1.29.0", "@opentelemetry/semantic-conventions": "1.28.0", "@sentry/node": "9.15.0", "@sentry/opentelemetry": "9.15.0", "react-retool": "1.4.0", "date-fns": "3.6.0"}}