const { baseServerlessConfiguration, getOTELEnv } = require('@dbd/serverless-environments/base');
const { environments } = require('@dbd/serverless-environments');

const env = environments[process.env.AWS_ENV ?? 'development'];

module.exports = (async () => {
  const serverlessConfig = {
    ...baseServerlessConfiguration,
    plugins: env.dataDogEnabled
      ? baseServerlessConfiguration.plugins?.concat(['serverless-plugin-datadog'])
      : baseServerlessConfiguration.plugins,
    service: 'residuals-service',
    package: {
      individually: true,
      patterns: [
        'src/lib/db/prisma-client/libquery_engine-linux-*',
        '!node_modules/prisma/libquery_engine-*',
        '!node_modules/@prisma/engines/**',
      ],
    },
    provider: {
      ...baseServerlessConfiguration.provider,
      apiGateway: {
        minimumCompressionSize: 1024,
        resourcePolicy: [
          {
            Effect: 'Deny',
            Principal: '*',
            Action: 'execute-api:Invoke',
            Resource: 'execute-api:/*',
            ...(env.residualsService.vpcEndpoint
              ? {
                  Condition: {
                    StringNotEquals: {
                      'aws:SourceVpce': env.residualsService.vpcEndpoint.vpcEndpointIds[0],
                    },
                  },
                }
              : {}),
          },
          {
            Effect: 'Allow',
            Principal: '*',
            Action: 'execute-api:Invoke',
            Resource: 'execute-api:/*',
          },
        ],
      },
      ...(env.residualsService.vpcEndpoint
        ? {
            endpointType: env.residualsService.vpcEndpoint.endpointType,
            vpcEndpointIds: env.residualsService.vpcEndpoint.vpcEndpointIds,
          }
        : {}),
      environment: {
        ...getOTELEnv('residual-service'),
        NODE_OPTIONS: '--enable-source-maps',
        NODE_PATH: './:/opt/node_modules',
        NODE_ENV: process.env.NODE_ENV ?? 'development',
        LOG_LEVEL: 'debug',
        AWS_ENV: process.env.AWS_ENV,
        DATADOG_SITE: 'datadoghq.com',
        BUILD_VERSION: process.env.BUILD_VERSION,
        DATADOG_API_KEY_SECRET_ARN: process.env.DATADOG_API_KEY_SECRET_ARN ?? '',
        DATADOG_API_KEY: process.env.DD_API_KEY ?? '',
        DATADOG_APP_KEY: process.env.DATADOG_APP_KEY ?? '',
        POSTGRES_USER: env.warehouse.username,
        POSTGRES_PASSWORD: env.warehouse.password,
        POSTGRES_HOST: env.warehouse.host,
        POSTGRES_PORT: '5432',
        POSTGRES_DATABASE: env.warehouse.database,
        SENTRY_DSN: process.env.SENTRY_DSN,
      },
      iam: {
        role: {
          statements: [
            {
              Effect: 'Allow',
              Action: ['lambda:InvokeFunction', 'lambda:InvokeAsync'],
              Resource: '*',
            },
            {
              Effect: 'Allow',
              Action: [
                'dynamodb:GetItem',
                'dynamodb:Query',
                'dynamodb:PutItem',
                'dynamodb:UpdateItem',
                'dynamodb:BatchGetItem',
              ],
              Resource: '*',
            },
          ],
          managedPolicies: ['arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess'],
        },
      },
      ...(env.residualsService.vpc ? { vpc: env.residualsService.vpc } : {}),
    },
    custom: {
      ...baseServerlessConfiguration.custom,
      'serverless-offline': {
        lambdaPort: 3002,
        httpPort: 3003,
      },
      localstack: {
        accountID: '************',
        debug: true,
        stages: ['local'],
        host: 'http://localstack',
        edgePort: 4566,
        autostart: false,
        lambda: {
          mountCode: false,
        },
      },
      esbuild: {
        config: './esbuild.config.cjs',
      },
      warmup: {
        default: { enabled: false },
      },
      customDomains: [],
      // If datadog is enabled, add the datadog config to the custom section
      ...(env.dataDogEnabled
        ? {
            datadog: {
              ...baseServerlessConfiguration.custom?.datadog,
            },
          }
        : {}),
    },
    functions: {
      proxy: {
        handler: 'src/proxy.proxy',
        timeout: 30,
        memorySize: 2048, // 2GB
        warmup: { default: { enabled: false } },
        environment: {
          AUTH_DOMAIN: env.fusionAuthDomain,
          AUTH_ISSUER_DOMAIN: env.fusionAuthIssuerDomain,
          ACCOUNTS_SERVICE_URL: env.accountServiceUrl,
          BASE_PAYMENT_SERVICE_URL: env.paymentServiceUrl,
          FILE_SERVICE_URL: env.fileServiceUrl,
          CLIENT_ID: env.clientId,
          CLIENT_SECRET: env.clientSecret,
          FUSIONAUTH_URL: env.fusionAuthUrl,
          MICROSERVICE_MASTER_ENTITY_ID: env.microserviceMasterEntityId,
          RESIDUALS_DATABASE_URL: env.residualsService.databaseUrl,
          DYNAMODB_URL: env.dynamo.endpoint,
          DYNAMODB_TABLE_NAME: env.dynamo.paymentsCoreTableName,
          FLAGSMITH_ENVIRONMENT_ID: env.flagsmith.environmentId,
          FLAGSMITH_API_URL: env.flagsmith.apiUrl,
          DYNAMODB_EVENTS_TABLE_NAME: env.dynamo.eventsTableName,
          API_HOST: '',
          API_PORT: '',
        },
        events: [
          {
            http: {
              method: 'any',
              path: '/',
              private: false,
            },
          },
          {
            http: {
              method: 'any',
              path: '/{proxy+}',
              private: false,
            },
          },
        ],
      },
      processResidualPeriod: {
        handler: 'src/handlers.processResidualPeriod',
        name: 'residuals-processResidualPeriod',
        timeout: 60 * 15, // 15 minutes
        memorySize: 2048, // 2GB
        warmup: {
          default: { enabled: false },
        },
        environment: {
          BUILD_VERSION: process.env.BUILD_VERSION ?? 'dev',
          SENTRY_DSN: process.env.SENTRY_DSN,
          NODE_ENV: process.env.NODE_ENV ?? 'development',
          LOG_LEVEL: 'info',
          RESIDUALS_DATABASE_URL: env.residualsService.databaseUrl,
          DYNAMODB_URL: env.dynamo.endpoint,
          DYNAMODB_TABLE_NAME: env.dynamo.paymentsCoreTableName,
          FLAGSMITH_ENVIRONMENT_ID: env.flagsmith.environmentId,
          FLAGSMITH_API_URL: env.flagsmith.apiUrl,
          DYNAMODB_EVENTS_TABLE_NAME: env.dynamo.eventsTableName,
        },
      },
    },
  };
  return serverlessConfig;
})();
