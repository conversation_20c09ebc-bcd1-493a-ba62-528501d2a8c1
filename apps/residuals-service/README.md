# Residuals Service
This stack contains an fastify serverless api for residuals and lambdas for ingesting residual data from the data warehouse.

```shell
nx slsBuild residuals-service --stage staging
nx slsRemove residuals-service --stage staging
```

## Run Locally TODO
Set the following ENV vars
```
LOCALSTACK=true
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=key
AWS_SECRET_ACCESS_KEY=key
RESIDUALS_DATABASE_URL=mysql://user:password@host:3306/residuals"
```

```
localstack start
pnx run residuals-service:seed
pnx run residuals-service:serve

```


### Residuals Endpoints TODO


## Deploy
```shell
LOCALSTACK=false NODE_ENV=staging aws-vault exec development-account -- nx run residuals-service:slsDeploy --stage staging
AWS_REGION=us-east-1 aws-vault exec customer-sandbox-account -- nx run residuals-service:slsDeploy:sandbox
```

## Deploy To AWS

```shell
AWS_REGION=us-east-1 aws-vault exec development-account -- bun nx run residuals-service:slsBuild:development
AWS_REGION=us-east-1 aws-vault exec development-account -- bun nx run residuals-service:slsDeploy:development
AWS_REGION=us-east-1 aws-vault exec customer-sandbox-account -- bun nx run residuals-service:slsDeploy:sandbox
BUILD_VERSION=v2.30.4-1 AWS_REGION=us-east-1 aws-vault exec production-account -- bun nx run residuals-service:slsDeploy:production
```

# Testing
You need a local instance of mysql running to run the test suite.

```shell
docker compose -f tools/docker-compose.residuals.yaml up
```

## Push Containers
```shell
aws-vault exec shared-services-account -- aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
doppler run -p forward-apps -c prd -- bun nx run residuals-service:container:production
```