import { PlaidEnvironments } from 'plaid';

import type { Environment } from './environment.types.js';
const AWS_ACCOUNT_ID = '************';
const FWD_TENANT_ID = 'tnt_2MvQjC8bzx3RLPT6si00auqLOjU';
const FISERV_TENANT_ID = 'tnt_2MvQlZSIR823Wh0NXPRjCUMiorb';

// TODO: This was removed from core-types because of ESM conversion issues.
enum SnowflakeExportRecordTypeEnum {
  Funding = 'funding',
  Settlement = 'settlement',
  Auth = 'auth',
  Dispute = 'dispute',
}

export const env: Environment = {
  name: 'production',
  profile: '',
  region: 'us-east-1',
  dataDogEnabled: true,
  otelExporter: 'none',
  accountId: AWS_ACCOUNT_ID,
  deploymentRole: `arn:aws:iam::${AWS_ACCOUNT_ID}:role/sls-cf-executor-role`,
  vpc: {
    securityGroupIds: ['sg-073c44ff49e8e605d'],
    subnetIds: ['subnet-076a1d1fa3adc9dc1', 'subnet-005f7a5f021084898', 'subnet-0601ca6510d2a32b8'],
  },
  dynamo: {
    endpoint: 'https://dynamodb.us-east-1.amazonaws.com',
    eventsTableName: `events-table`,
    paymentsCoreTableName: `payments-core-table`,
    riskServiceTableName: `risk-service-table`,
    reportsTableName: 'reports-table',
    vaultTableName: 'vault-table',
  },
  residualsService: {
    databaseUrl: process.env.RESIDUALS_DATABASE_URL ?? '',
    vpcEndpoint: {
      endpointType: 'PRIVATE',
      vpcEndpointIds: ['vpce-0260609362a378b9c'],
    },
    vpc: {
      id: 'vpc-063ab960d4347a322',
      securityGroupIds: ['sg-0cba89f078c8afd73'],
      subnetIds: ['subnet-0601ca6510d2a32b8', 'subnet-005f7a5f021084898', 'subnet-076a1d1fa3adc9dc1'],
    },
  },
  plaid: {
    plaidEnv: PlaidEnvironments.production,
    tenantPlaidConfigData: process.env.TENANT_PLAID_CONFIG_DATA ?? '{}',
    webhookTopicName: 'production-us-east-1-plaid-webhook-received',
  },
  copilotSettings: {
    copilotPassword: process.env.COPILOT_PASSWORD ?? '',
  },
  decisioning: {
    slack: {
      boardingErrorWebhookUrl: process.env.SLACK_BOARDING_ERROR_WEBHOOK_URL ?? '',
      dailyMonitoringWebhookUrl: process.env.SLACK_DAILIY_MONITORING_WEBHOOK_URL ?? '',
    },
    boarding: {
      snsErrors: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-boarding_errored`,
      },
    },
  },
  grailpay: {
    forwardBaseUrl: process.env.GRAILPAY_FORWARD_BASE_URL ?? '',
    forwardSecret: process.env.GRAILPAY_FORWARD_SECRET ?? '',
    forwardProcessorSecret: process.env.GRAILPAY_FORWARD_PROCESSOR_SECRET ?? '',
    fiservBaseUrl: process.env.GRAILPAY_FISERV_BASE_URL ?? '',
    fiservSecret: process.env.GRAILPAY_FISERV_SECRET ?? '',
    fiservProcessorSecret: process.env.GRAILPAY_FISERV_PROCESSOR_SECRET ?? '',
    mockGrailpay: false,
    tenantsBankGatewayIds:
      '{"tnt_2MvQjC8bzx3RLPT6si00auqLOjU":"Forward-Grailpay", "tnt_2MvQlZSIR823Wh0NXPRjCUMiorb":"Fiserv-Grailpay"}',
    tenantVendorMap:
      '{"vendor-id-1":"tnt_2MvQjC8bzx3RLPT6si00auqLOjU", "vendor-id-2":"tnt_2MvQlZSIR823Wh0NXPRjCUMiorb"}',
  },
  fiserv: {
    marketplaceApiUrl: 'https://connect.fiservapis.com/unified',
    monthlyReportBucket: 'fwd-sftp-push-fiserv-production',
  },
  sftp: {
    awsEndpoint: process.env.AWS_ENDPOINT ?? '',
    pushRetrySchedule: 'rate(1 hour)',
    configurations: [
      {
        id: 'productionCrossRiver',
        pull: {
          name: 'crossRiver',
          bucket: 'fwd-cross-river-production',
          password: process.env.CROSS_RIVER_SFTP_PASSWORD,
          host: 'sftp01.crbnj.com',
          port: 22,
          username: 'dbdventures.sftp',
          paths: [
            {
              source_path: '/DBDVentures/PROD/COS/DailyReports',
              destination_path: 'DBDVentures/PROD/COS/DailyReports',
            },
          ],
          schedule: 'rate(4 hours)',
        },
        push: {
          name: 'crossRiver',
          bucket: 'fwd-cross-river-push-production',
          useExistingBucket: false,
          password: process.env.CROSS_RIVER_SFTP_PASSWORD,
          host: 'sftp01.crbnj.com',
          port: 22,
          username: 'dbdventures.sftp',
          paths: [
            {
              sftp_root: '/DBDVentures/PROD/COS/DailyReports',
              source_path: '/DBDVentures/PROD/COS/DailyReports',
              destination_path: '',
            },
          ],
        },
      },
      {
        id: 'fiservPush',
        push: {
          name: 'fiservPush',
          bucket: 'fwd-sftp-push-fiserv-production',
          useExistingBucket: false,
          password: process.env.FISERV_SFTP_PASSWORD,
          AwsSecretsPrivateKeyName: 'fiserv/sftp/private_key', // The name of the secret in AWS Secrets Manager for using as the private key in the sftp connection
          host: 'prod2-gw-na.firstdataclients.com',
          port: 6522,
          username: 'PROD-NAGW-TMECH001',
          paths: [
            {
              sftp_root: '/', // The root folder of the sftp server
              source_path: 'forwardMonthlyReport', // The path in the s3 bucket
              destination_path: '', // The path in the sftp server
            },
          ],
        },
      },
    ],
  },
  flagsmith: {
    environmentId: process.env.FLAGSMITH_ENVIRONMENT_ID ?? '',
    apiUrl: 'https://flags.getfwd.com/api/v1/',
  },
  sns: {
    partnerCreation: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-partner_created`,
    },
    accountCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_created`,
    },
    accountUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_updated`,
    },
    terminalIdCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_terminal_id_created`,
    },
    applicationStatuses: {
      canceled: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_cancelled`,
      },
      approved: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_approved`,
      },
      manuallyApproved: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_manually_approved`,
      },
      manuallyDeclined: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_manually_declined`,
      },
      statusChanged: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_status_changed`,
      },
      updated: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_updated`,
      },
      submitted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_submitted`,
      },
      needInformation: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_need_information`,
      },
      deleted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_boarding_deleted`,
      },
      payeeApplicationSubmitted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-boarding_payee_application_submitted`,
      },
      payeeApplicationUnderReview: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-boarding_payee_application_under_review`,
      },
    },
    disputeCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-disputeCreated`,
    },
    disputeAccepted: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-disputeAccepted`,
    },
    disputeContested: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-disputeContested`,
    },
    disputeUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-disputeUpdated`,
    },
    merchantPayoutsDisabled: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_payouts_disabled`,
    },
    merchantPayoutsEnabled: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_payouts_enabled`,
    },
    bankAccountValidated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-bank_account_validated`,
    },
    bankAccountVerified: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-bank_account_verified`,
    },
    contractorCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-contractor_created`,
    },
    contractorActivated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-contractor_activated`,
    },
    contractorDeactivated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-contractor_deactivated`,
    },
    allEventsTopic: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-all-events-topic`,
    },
    paymentGatewaySettingsUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_gateway_updated`,
    },
    analyticsRequestCreatedTopic: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-analytics_request_created`,
    },
  },
  sqs: {
    crossRiverWebhookEvents: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-cross-river-webhook-queue`,
    },
    decisionInbound: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:decisioning-all-events`,
    },
    grailpayWebhookEvents: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-grailpay-webhook-queue`,
    },
    releaseGrossFundingHolds: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-release-gross-funding-holds`,
      url: `https://sqs.us-east-1.amazonaws.com/${AWS_ACCOUNT_ID}/production-us-east-1-release-gross-funding-holds`,
    },
    grailpayWebhookEventsQueueUrl: `https://sqs.us-east-1.amazonaws.com/${AWS_ACCOUNT_ID}/serverless-core-grailpay-webhook-queue`,
    taktileQueueArn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-taktile-webhook-received`,
    notificationDeliveryCreatedArn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-notification-delivery-created`,
  },
  svix: {
    svixApiKey: process.env.SVIX_API_KEY ?? '',
    svixEnabled: 'true',
  },
  customDomains: {
    core: [
      {
        domainName: 'api2.getfwd.com',
        basePath: '',
        enabled: true,
      },
      {
        domainName: 'webhooks.getfwd.com',
        basePath: '',
        enabled: true,
        certificateArn: `arn:aws:acm:us-east-1:${AWS_ACCOUNT_ID}:certificate/03292b71-826b-4d61-93f3-40b21aef005b`,
      },
    ],
    decisioning: [
      {
        basePath: 'decisioning',
        domainName: 'api2.getfwd.com',
        enabled: true,
      },
    ],
    settlement: [
      {
        domainName: 'api2.getfwd.com',
        basePath: '',
        enabled: true,
      },
    ],
    notifications: [
      {
        domainName: 'api2.getfwd.com',
        basePath: 'notifications',
        enabled: true,
      },
    ],
  },
  settlement: {
    tenants: [
      {
        name: 'fiserv',
        id: FISERV_TENANT_ID,
        email: '<EMAIL>',
        timeZero: '2023-05-01T04:00:00.000Z',
      },
      {
        name: 'dbd',
        id: FWD_TENANT_ID,
        email: '<EMAIL>',
        timeZero: '2024-01-12T04:00:00.000Z',
      },
    ],
    exportSchedules: [
      { type: SnowflakeExportRecordTypeEnum.Auth, schedule: 'cron(1 * * * ? *)' },
      { type: SnowflakeExportRecordTypeEnum.Settlement, schedule: 'cron(11 * * * ? *)' },
      { type: SnowflakeExportRecordTypeEnum.Funding, schedule: 'cron(21 * * * ? *)' },
      { type: SnowflakeExportRecordTypeEnum.Dispute, schedule: 'cron(31 * * * ? *)' },
    ],
    snowflake: {
      account: 'kfb28717',
      username: process.env.SNOWFLAKE_SERVICE_USER ?? '',
      privateKey: process.env.SNOWFLAKE_SERVICE_KEY ?? '',
      database: 'fwd_production',
      schema: 'data',
      application: 'settlement-production',
      warehouse: 'compute_wh',
      role: 'app_service_role',
    },
    s3: {
      bucketName: 'dbdventures-sf-stage-production',
      useExistingBucket: true,
    },
    slack: {
      webhookUrl: process.env.SLACK_SETTLEMENT_WEBHOOK_URL ?? '',
    },
    sns: { topicName: 'settlement-failed' },
  },
  settlementQueue: {
    stateMachineArn: `arn:aws:states:us-east-1:${AWS_ACCOUNT_ID}:stateMachine:settlementSettleQueueProcess-production`,
  },
  settlementPayout: {
    stateMachineArn: `arn:aws:states:us-east-1:${AWS_ACCOUNT_ID}:stateMachine:settlementPayoutProcess-production`,
    tenantACHData: process.env.TENANT_ACH_DATA ?? '{}',
    tenantOriginatorData: process.env.TENANT_ORIGINATOR_DATA ?? '{}',
    vaultApiKey: process.env.VAULT_API_KEY ?? '',
    parallelProcessBatchSize: process.env.PARALLEL_PROCESS_BATCH_SIZE ?? '10',
  },
  accountCreatedResources: {
    crossRiverEnabled: 'true',
    defaultCheckoutSettingDomain: 'https://checkout.pci.getfwd.com',
  },
  crossRiver: {
    crossRiverTenantMasterAccountNumbers: process.env.CROSS_RIVER_TENANT_MASTER_ACCOUNT_NUMBERS ?? '',
    crossRiverTenantSettlementAccountNumbers: process.env.CROSS_RIVER_TENANT_SETTLEMENT_ACCOUNT_NUMBERS ?? '',
    crossRiverTenantAchSettlementAccountNumbers: process.env.CROSS_RIVER_TENANT_ACH_SETTLEMENT_ACCOUNT_NUMBERS ?? '',
    crossRiverPartnerId: process.env.CROSS_RIVER_PARTNER_ID ?? '',
    crossRiverClientId: process.env.CROSS_RIVER_CLIENT_ID ?? '',
    crossRiverClientSecret: process.env.CROSS_RIVER_CLIENT_SECRET ?? '',
    crossRiverSigningSecret: process.env.CROSS_RIVER_SIGNING_SECRET ?? '',

    crossRiverAuthUrl: process.env.CROSS_RIVER_AUTH_URL ?? '',
    crossRiverCoreUrl: 'https://api.crbcos.com/CORE/v1',
    crossRiverCoreAchUrl: 'https://api.crbcos.com/ach/v1',
    crossRiverWebhookUrl: 'https://api.crbcos.com/webhooks/v1',

    useProxy: 'false',
    crEnabled: 'true',
    crPartnerWhiteList: '[]',
    useMockAchReceiver: 'false',
  },
  retoolUrl: 'https://retool.getfwd.com',
  taktile: {
    webhookApiKey: process.env.TAKTILE_WEBHOOK_SECRET ?? '',
  },
  ticketingService: {
    baseUrl: 'https://forward.freshdesk.com',
    apiKey: process.env.FRESHDESK_API_KEY ?? '',
    freshdeskEnabled: 'true',
    freshdeskEnvironment: 'Production',
  },
  accountServiceUrl: 'http://account-service.getfwd.net',
  boardingServiceUrl: 'http://boarding-service.getfwd.net',
  decisioningServiceUrl: 'http://decisioning-service.getfwd.net',
  notificationsServiceUrl: 'https://notifications-service.getfwd.net',
  paymentServiceUrl: 'http://payments-core-service.getfwd.net',
  fileServiceUrl: 'http://file-service.getfwd.net',
  clientId: '********-0000-0000-0001-************',
  clientSecret: process.env.CLIENT_SECRET ?? '',
  fusionAuthUrl: 'https://auth.getfwd.com',
  fusionAuthDomain: process.env.AUTH_DOMAIN ?? '',
  fusionAuthIssuerDomain: process.env.AUTH_ISSUER_DOMAIN ?? '',
  microserviceMasterEntityId: '********-0000-0000-0000-************',
  tenantIdList: `{"${FWD_TENANT_ID}":"https://portal.getfwd.com","${FISERV_TENANT_ID}":"https://isvportal.fiserv.com"}`,
  Resources: {},
  blindIndexEncryptionSecretName: process.env.BLIND_INDEX_ENCRYPTION_SECRET_NAME ?? '',
  blindIndexEncryptionSalt: process.env.BLIND_INDEX_ENCRYPTION_SALT ?? '',
  coreResources: {},
  settlementResources: {},
  sentryDsn: 'https://<EMAIL>/4507261584146432',
  riskServiceFusionAuth: {
    clientId: '********-0000-0000-0001-000000000011',
    clientSecret: process.env.RISK_SERVICE_CLIENT_SECRET ?? '',
  },
  vault: {
    vpcEndpoint: {
      endpointType: 'PRIVATE',
      vpcEndpointIds: ['vpce-0260609362a378b9c'],
    },
    vpc: {
      // id: 'vpc-063ab960d4347a322',
      securityGroupIds: ['sg-0cba89f078c8afd73'],
      subnetIds: ['subnet-0601ca6510d2a32b8', 'subnet-005f7a5f021084898', 'subnet-076a1d1fa3adc9dc1'],
    },
    kmsKeyId: 'alias/vault-key',
    kmsKeyVersion: '1',
    resources: {
      Resources: {},
    },
    tenantId: 'vtnt_2gCmiDXdeikam90b116LXFBu7Tl',
    apiUrl: 'https://df47ypuo0e.execute-api.us-east-1.amazonaws.com/production',
    clientId: '********-0000-0000-0001-000000000007',
    clientSecret: process.env.CLIENT_SECRET ?? '',
  },
  warehouse: {
    username: 'analytics_event_user',
    password: process.env.WAREHOUSE_DB_PASSWORD ?? '',
    host: 'aurora-postgresql-production-3.cuxarnlutr0k.us-east-1.rds.amazonaws.com',
    database: 'warehouse',
  },
};
