import type { Vpc } from 'serverless/aws.js';
import Aws from 'serverless/aws.js';

// TODO: This was removed from core-types because of ESM conversion issues.
enum SnowflakeExportRecordTypeEnum {
  Funding = 'funding',
  Settlement = 'settlement',
  Auth = 'auth',
  Dispute = 'dispute',
}

type SftpConfiguration = {
  id: string;
  pull?: {
    name: string;
    bucket: string;
    username: string;
    password?: string;
    AwsSecretsPrivateKeyName?: string;
    host: string;
    port: number;
    paths: [
      {
        source_path: string;
        destination_path: string;
      },
    ];
    schedule: string;
  };
  push?: {
    name: string;
    bucket: string;
    useExistingBucket: boolean;
    username: string;
    password?: string;
    AwsSecretsPrivateKeyName?: string;
    host: string;
    port: number;
    paths: [
      {
        sftp_root: string;
        source_path: string;
        destination_path: string;
      },
    ];
  };
};

export interface Environment {
  name: 'development' | 'staging' | 'production' | 'sandbox' | 'local';
  region: string;
  profile?: string;
  accountId: string;
  deploymentRole: string;
  fusionAuthDomain?: string;
  fusionAuthIssuerDomain?: string;
  dataDogEnabled?: boolean;
  otelExporter: 'custom' | 'coralogix' | 'lumigo' | 'none' | 'otel' | 'honeycomb';
  dynamo: {
    endpoint?: string;
    eventsTableName: string;
    paymentsCoreTableName: string;
    riskServiceTableName: string;
    reportsTableName: string;
    vaultTableName: string;
  };
  residualsService: {
    databaseUrl: string;
    vpcEndpoint?: {
      endpointType: 'PRIVATE';
      vpcEndpointIds: string[];
    };
    vpc?: Vpc & { id: string };
  };
  plaid: PlaidEnvironment;
  copilotSettings: {
    copilotPassword: string;
    copilotTestMerchantId?: string;
  };
  decisioning: DecisioningEnvironment;
  grailpay?: {
    forwardBaseUrl: string;
    forwardSecret: string;
    forwardProcessorSecret: string;
    fiservBaseUrl: string;
    fiservSecret: string;
    fiservProcessorSecret: string;
    tenantsBankGatewayIds: string;
    mockGrailpay: boolean;
    tenantVendorMap: string;
  };
  fiserv: {
    marketplaceApiUrl: string;
    monthlyReportBucket?: string;
  };
  sftp: {
    awsEndpoint: string;
    pushRetrySchedule: string;
    configurations: SftpConfiguration[];
  };
  flagsmith: FlagsmithEnvironment;
  sns: {
    accountCreated: Aws.Sns;
    accountUpdated: Aws.Sns;
    allEventsTopic: Aws.Sns;
    analyticsRequestCreatedTopic: Aws.Sns;
    applicationStatuses: {
      approved: Aws.Sns;
      canceled: Aws.Sns;
      deleted: Aws.Sns;
      manuallyApproved: Aws.Sns;
      manuallyDeclined: Aws.Sns;
      needInformation: Aws.Sns;
      payeeApplicationSubmitted: Aws.Sns;
      payeeApplicationUnderReview: Aws.Sns;
      statusChanged: Aws.Sns;
      submitted: Aws.Sns;
      updated: Aws.Sns;
    };
    bankAccountValidated: Aws.Sns;
    bankAccountVerified: Aws.Sns;
    contractorActivated: Aws.Sns;
    contractorCreated: Aws.Sns;
    contractorDeactivated: Aws.Sns;
    disputeAccepted: Aws.Sns;
    disputeContested: Aws.Sns;
    disputeCreated: Aws.Sns;
    disputeUpdated: Aws.Sns;
    merchantPayoutsDisabled: Aws.Sns;
    merchantPayoutsEnabled: Aws.Sns;
    partnerCreation: Aws.Sns;
    paymentGatewaySettingsUpdated: Aws.Sns;
    terminalIdCreated: Aws.Sns;
  };
  sqs?: {
    crossRiverWebhookEvents: Aws.Sqs;
    decisionInbound: Aws.Sqs;
    grailpayWebhookEvents: Aws.Sqs;
    releaseGrossFundingHolds: Aws.Sqs & { url: string };
    grailpayWebhookEventsQueueUrl: string;
    taktileQueueArn: string;
    notificationDeliveryCreatedArn: string;
  };
  svix: SvixEnvironment;
  customDomains: {
    core: {
      domainName: string;
      basePath: string;
      enabled: boolean;
      certificateArn?: string;
      certificateName?: string;
      createRoute53Record?: boolean;
    }[];
    decisioning: {
      domainName: string;
      basePath: string;
      enabled: boolean;
      certificateArn?: string;
      certificateName?: string;
      createRoute53Record?: boolean;
    }[];
    settlement: {
      domainName: string;
      basePath: string;
      enabled: boolean;
      certificateArn?: string;
      certificateName?: string;
      createRoute53Record?: boolean;
    }[];
    notifications: {
      domainName: string;
      basePath: string;
      enabled: boolean;
      certificateArn?: string;
      certificateName?: string;
      createRoute53Record?: boolean;
    }[];
  };
  settlement: SettlementEnvironment;
  settlementQueue?: {
    stateMachineArn: string;
  };
  accountCreatedResources: {
    crossRiverEnabled: 'true' | 'false';
    defaultCheckoutSettingDomain: string;
    defaultMockGatewayName?: string;
  };
  settlementPayout?: SettlementPayoutsEnvironment;
  crossRiver: CrossRiverEnvironment;
  taktile: {
    webhookApiKey: string;
  };
  ticketingService: {
    baseUrl: string;
    apiKey: string;
    freshdeskEnabled: string;
    freshdeskEnvironment: 'Test' | 'Production';
  };
  retoolUrl: string;
  accountServiceUrl: string;
  boardingServiceUrl: string;
  decisioningServiceUrl: string;
  notificationsServiceUrl: string;
  paymentServiceUrl: string;
  fileServiceUrl: string;
  clientId: string;
  clientSecret: string;
  fusionAuthUrl: string;
  microserviceMasterEntityId: string;
  tenantIdList: string; // JSON object
  vpc?: Vpc;
  Resources: any;
  blindIndexEncryptionSecretName?: string;
  blindIndexEncryptionSalt?: string;
  coreResources: any;
  settlementResources: any;
  //Risk service fusionauth:
  riskServiceFusionAuth: {
    clientId: string;
    clientSecret: string;
  };
  vault: {
    vpcEndpoint: {
      endpointType: 'PRIVATE';
      vpcEndpointIds: string[];
    };
    vpc: Vpc & { id?: string };
    kmsKeyId: any;
    kmsKeyVersion: string;
    resources: any;
    tenantId: string;
    clientId: string;
    clientSecret: string;
    apiUrl: string;
  };
  sentryDsn: string;
  warehouse: {
    username: string;
    password: string;
    host: string;
    writeHost?: string;
    database: string;
  };
}

export interface PlaidEnvironment {
  plaidEnv: string;
  // JSON stringified object of tenant PlaidConfig, Record<string,Omit<PlaidConfig, 'plaidEnv'>>
  // {'tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG': {plaidClientId: '123', plaidClientSecret: '123'}}
  tenantPlaidConfigData: string;
  webhookTopicName: string;
}

export interface FlagsmithEnvironment {
  environmentId: string; //FLAGSMITH_ENVIRONMENT_ID,
  apiUrl: string; //process.env.FLAGSMITH_API_URL,
}

export interface SettlementEnvironment {
  tenants: { name: string; id: string; email?: string; timeZero?: string }[];
  exportSchedules?: { type: SnowflakeExportRecordTypeEnum; schedule: string }[];
  snowflake: {
    account: string;
    username: string;
    privateKey: string;
    application: string;
    warehouse: string;
    database: string;
    schema: string;
    role?: string;
  };
  s3: {
    bucketName: string;
    useExistingBucket: boolean;
  };
  slack: {
    webhookUrl: string;
  };
  sns: Aws.Sns;
}

export interface SettlementPayoutsEnvironment {
  stateMachineArn: string;

  // JSON stringified object of tenant ACHEntityData, Record<string,ACHEntityData>
  // {'tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG': {accountNumber: '123', accountNumberMask: '123***123' routingNumber: '123', name: 'DBD tenant bank account', accountType: ACHEntityAccountTypeEnum.Checking}}
  tenantACHData: string;

  // JSON stringified object of tenant ACHOriginatorData, Record<string,ACHOriginatorData>
  // {'tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG': {name: 'Forward Tenant', identification: '**********'}}
  tenantOriginatorData: string;
  vaultApiKey: string;

  // Size of payouts batch to process in parallel
  parallelProcessBatchSize?: string;
}

export interface SvixEnvironment {
  svixApiKey: string;
  svixEnabled: string;
}

export interface CrossRiverEnvironment {
  // JSON stringified object of tenant CR account numbers, Record<string,string>
  // {'tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG': '123'}
  crossRiverTenantMasterAccountNumbers: string;
  // JSON stringified object of tenant CR account numbers, Record<string,string>
  // {'tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG': '123'}
  crossRiverTenantSettlementAccountNumbers: string;
  // JSON stringified object of tenant CR ACH LEDGER account numbers, Record<string,string>
  // {'tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG': '123'}
  crossRiverTenantAchSettlementAccountNumbers?: string;

  crossRiverTenantSettlementSubledgerAccountNumbers?: string;

  crossRiverPartnerId: string;

  crossRiverClientId: string;
  crossRiverClientSecret: string;
  crossRiverSigningSecret: string;
  crossRiverAuthUrl: string;
  crossRiverCoreUrl: string;
  crossRiverCoreAchUrl: string;
  crossRiverWebhookUrl: string;

  useProxy: string;
  proxyProtocol?: string;
  proxyHost?: string;
  proxyPort?: number;
  proxyUserName?: string;
  proxyPassword?: string;

  crEnabled: string;
  crPartnerWhiteList: string;

  useMockAchReceiver: string;
}

export interface DecisioningEnvironment {
  slack: {
    boardingErrorWebhookUrl: string;
    dailyMonitoringWebhookUrl: string;
  };
  boarding: {
    snsErrors: Aws.Sns;
  };
}
