import { PlaidEnvironments } from 'plaid';

import type { Environment } from './environment.types.js';
const AWS_ACCOUNT_ID = '************';
const FWD_TENANT_ID = 'tnt_2MvQjC8bzx3RLPT6si00auqLOjU';
const FISERV_TENANT_ID = 'tnt_2MvQlZSIR823Wh0NXPRjCUMiorb';

export const env: Environment = {
  name: 'development',
  profile: '',
  region: 'us-east-1',
  accountId: AWS_ACCOUNT_ID,
  dataDogEnabled: false, // if true, otelExporter must be set to 'none'
  otelExporter: 'custom',
  deploymentRole: `arn:aws:iam::${AWS_ACCOUNT_ID}:role/sls-cf-executor-role`,
  fusionAuthDomain: 'https://auth.dev.getfwd.com',
  fusionAuthIssuerDomain: 'https://auth.getfwd.com',
  vpc: {
    securityGroupIds: ['sg-00b9f10fbeb9fd3e8'],
    subnetIds: ['subnet-0dd3156d87da9820c', 'subnet-04e01ec4f55300336', 'subnet-0d945cf4d5f8ad548'],
  },
  dynamo: {
    endpoint: 'https://dynamodb.us-east-1.amazonaws.com',
    eventsTableName: `events-table`,
    paymentsCoreTableName: `payments-core-table`,
    riskServiceTableName: `risk-service-table`,
    reportsTableName: 'reports-table',
    vaultTableName: 'vault-table',
  },
  residualsService: {
    databaseUrl: process.env.RESIDUALS_DATABASE_URL ?? '',
    vpcEndpoint: {
      endpointType: 'PRIVATE',
      vpcEndpointIds: ['vpce-081a8293801cb184a'],
    },
    vpc: {
      id: 'vpc-0ef8e78d2e56f29b3',
      securityGroupIds: ['sg-034798982de5a8092'],
      subnetIds: ['subnet-0dd3156d87da9820c', 'subnet-04e01ec4f55300336', 'subnet-0d945cf4d5f8ad548'],
    },
  },
  plaid: {
    plaidEnv: PlaidEnvironments.sandbox,
    tenantPlaidConfigData: process.env.TENANT_PLAID_CONFIG_DATA ?? '{}',
    webhookTopicName: 'development-us-east-1-plaid-webhook-received',
  },
  copilotSettings: {
    copilotPassword: process.env.COPILOT_PASSWORD ?? '',
    copilotTestMerchantId: process.env.COPILOT_TEST_MERCHANT_ID ?? '',
  },
  decisioning: {
    slack: {
      boardingErrorWebhookUrl: process.env.SLACK_BOARDING_ERROR_WEBHOOK_URL ?? '',
      dailyMonitoringWebhookUrl: '',
    },
    boarding: {
      snsErrors: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-boarding_errored`,
      },
    },
  },
  grailpay: {
    forwardBaseUrl: process.env.GRAILPAY_FORWARD_BASE_URL ?? '',
    forwardSecret: process.env.GRAILPAY_FORWARD_SECRET ?? '',
    forwardProcessorSecret: process.env.GRAILPAY_FORWARD_PROCESSOR_SECRET ?? '',
    fiservBaseUrl: process.env.GRAILPAY_FISERV_BASE_URL ?? '',
    fiservSecret: process.env.GRAILPAY_FISERV_SECRET ?? '',
    fiservProcessorSecret: process.env.GRAILPAY_FISERV_PROCESSOR_SECRET ?? '',
    mockGrailpay: true,
    tenantsBankGatewayIds:
      '{"tnt_2MvQjC8bzx3RLPT6si00auqLOjU":"Forward-Grailpay", "tnt_2MvQlZSIR823Wh0NXPRjCUMiorb":"Forward-Grailpay"}',
    tenantVendorMap:
      '{"vendor-id-1":"tnt_2MvQjC8bzx3RLPT6si00auqLOjU", "vendor-id-2":"tnt_2MvQlZSIR823Wh0NXPRjCUMiorb"}',
  },
  fiserv: {
    marketplaceApiUrl: 'https://connect.uat.fiservapis.com/unified',
    monthlyReportBucket: 'forward-monthly-report-dev',
  },
  sftp: {
    awsEndpoint: process.env.AWS_ENDPOINT ?? '',
    pushRetrySchedule: 'rate(1 hour)',
    configurations: [
      {
        id: 'devCrossRiver',
        pull: {
          name: 'crossRiver',
          bucket: 'fwd-sftp-pull-development-cr',
          password: process.env.CROSS_RIVER_SFTP_PASSWORD,
          host: 'sftp01.crbnj.com',
          port: 22,
          username: 'dbdventures.sftp',
          paths: [
            {
              source_path: '/DBDVentures/SANDBOX/COS/DailyReports',
              destination_path: 'DBDVentures/SANDBOX/COS/DailyReports',
            },
          ],
          schedule: 'rate(4 hours)',
        },
      },
      {
        id: 'fiservPush',
        push: {
          name: 'fiservPush',
          bucket: 'forward-monthly-report-dev',
          useExistingBucket: false,
          password: process.env.FISERV_SFTP_PASSWORD,
          AwsSecretsPrivateKeyName: 'fiserv/sftp/private_key', // The name of the secret in AWS Secrets Manager for using as the private key in the sftp connection
          host: 'test2-gw-na.firstdataclients.com',
          port: 6522,
          username: 'CAT-NAGW-TMECH001',
          paths: [
            {
              sftp_root: '/', // The root folder of the sftp server
              source_path: 'forwardMonthlyReport', // The path in the s3 bucket
              destination_path: '', // The path in the sftp server
            },
          ],
        },
      },
    ],
  },
  flagsmith: {
    environmentId: process.env.FLAGSMITH_ENVIRONMENT_ID ?? '',
    apiUrl: process.env.FLAGSMITH_API_URL ?? 'https://flags.getfwd.com/api/v1/',
  },
  sns: {
    partnerCreation: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-partner_created`,
    },
    accountCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_created`,
    },
    accountUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_updated`,
    },
    terminalIdCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_terminal_id_created`,
    },
    applicationStatuses: {
      canceled: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_cancelled`,
      },
      approved: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_approved`,
      },
      manuallyApproved: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_manually_approved`,
      },
      manuallyDeclined: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_manually_declined`,
      },
      statusChanged: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_status_changed`,
      },
      updated: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_updated`,
      },
      submitted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_submitted`,
      },
      needInformation: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_need_information`,
      },
      deleted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_boarding_deleted`,
      },
      payeeApplicationSubmitted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-boarding_payee_application_submitted`,
      },
      payeeApplicationUnderReview: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-boarding_payee_application_under_review`,
      },
    },
    disputeCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-disputeCreated`,
    },
    disputeAccepted: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-disputeAccepted`,
    },
    disputeContested: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-disputeContested`,
    },
    disputeUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-disputeUpdated`,
    },
    merchantPayoutsDisabled: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_payouts_disabled`,
    },
    merchantPayoutsEnabled: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_payouts_enabled`,
    },
    bankAccountValidated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-bank_account_validated`,
    },
    bankAccountVerified: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-bank_account_verified`,
    },
    contractorCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-contractor_created`,
    },
    contractorActivated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-contractor_activated`,
    },
    contractorDeactivated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-contractor_deactivated`,
    },
    allEventsTopic: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-all-events-topic`,
    },
    paymentGatewaySettingsUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-merchant_gateway_updated`,
    },
    analyticsRequestCreatedTopic: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-analytics_request_created`,
    },
  },
  sqs: {
    crossRiverWebhookEvents: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-cross-river-webhook-queue`,
    },
    decisionInbound: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:development-decisioning-all-events`,
    },
    grailpayWebhookEvents: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-grailpay-webhook-queue`,
    },
    releaseGrossFundingHolds: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-release-gross-funding-holds`,
      url: `https://sqs.us-east-1.amazonaws.com/${AWS_ACCOUNT_ID}/development-us-east-1-release-gross-funding-holds`,
    },
    grailpayWebhookEventsQueueUrl: `https://sqs.us-east-1.amazonaws.com/${AWS_ACCOUNT_ID}/serverless-core-grailpay-webhook-queue`,
    taktileQueueArn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-taktile-webhook-received`,
    notificationDeliveryCreatedArn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-notification-delivery-created-queue`,
  },
  svix: {
    svixApiKey: process.env.SVIX_API_KEY ?? '',
    svixEnabled: 'true',
  },
  customDomains: {
    core: [
      {
        domainName: 'webhooks.dev.getfwd.com',
        basePath: '',
        enabled: true,
        certificateName: 'webhooks.dev.getfwd.com',
        createRoute53Record: false,
      },
      {
        domainName: 'webhooks.getfwd.dev',
        basePath: '',
        enabled: true,
        certificateName: 'webhooks.getfwd.dev',
        createRoute53Record: false,
      },
    ],
    decisioning: [
      {
        basePath: 'decisioning',
        domainName: 'api2.dev.getfwd.com',
        enabled: true,
        certificateName: 'webhooks.dev.getfwd.com',
        createRoute53Record: false,
      },
    ],
    settlement: [
      {
        domainName: 'api2.dev.getfwd.com',
        basePath: '',
        enabled: true,
        certificateName: 'webhooks.dev.getfwd.com',
        createRoute53Record: false,
      },
    ],
    notifications: [
      {
        domainName: 'api2.dev.getfwd.com',
        basePath: 'notifications',
        enabled: true,
        certificateName: 'webhooks.dev.getfwd.com',
        createRoute53Record: false,
      },
    ],
  },
  settlement: {
    tenants: [
      {
        name: 'fiserv',
        id: FISERV_TENANT_ID,
        email: '<EMAIL>',
      },
      { name: 'dbd', id: FWD_TENANT_ID, email: '<EMAIL>' },
    ],
    snowflake: {
      account: 'kfb28717',
      username: process.env.SNOWFLAKE_SERVICE_USER ?? '',
      privateKey: process.env.SNOWFLAKE_SERVICE_KEY ?? '',
      database: 'dbd_dev',
      schema: 'data',
      application: 'settlement-dev',
      warehouse: 'compute_wh',
      role: 'app_service_role',
    },
    s3: {
      bucketName: 'fwd-settlement-files-stage-development',
      useExistingBucket: false,
    },
    slack: {
      webhookUrl: process.env.SLACK_SETTLEMENT_WEBHOOK_URL ?? '',
    },
    sns: { topicName: 'settlement-failed' },
  },
  settlementQueue: {
    stateMachineArn: `arn:aws:states:us-east-1:${AWS_ACCOUNT_ID}:stateMachine:settlementSettleQueueProcess-development`,
  },
  settlementPayout: {
    stateMachineArn: `arn:aws:states:us-east-1:${AWS_ACCOUNT_ID}:stateMachine:settlementPayoutProcess-development`,
    tenantACHData: process.env.TENANT_ACH_DATA ?? '{}',
    tenantOriginatorData: process.env.TENANT_ORIGINATOR_DATA ?? '{}',
    vaultApiKey: process.env.VAULT_API_KEY ?? '',
    parallelProcessBatchSize: process.env.PARALLEL_PROCESS_BATCH_SIZE ?? '10',
  },
  accountCreatedResources: {
    defaultMockGatewayName: 'mockGateway',
    crossRiverEnabled: 'true',
    defaultCheckoutSettingDomain: 'https://checkout-dev.pci.getfwd.com',
  },
  crossRiver: {
    crossRiverTenantMasterAccountNumbers: process.env.CROSS_RIVER_TENANT_MASTER_ACCOUNT_NUMBERS ?? '',
    crossRiverTenantSettlementAccountNumbers: process.env.CROSS_RIVER_TENANT_SETTLEMENT_ACCOUNT_NUMBERS ?? '',
    crossRiverTenantAchSettlementAccountNumbers: process.env.CROSS_RIVER_TENANT_ACH_SETTLEMENT_ACCOUNT_NUMBERS ?? '',
    crossRiverPartnerId: '826fa648-5cb1-4ddd-a12f-af4e01146b1d',
    crossRiverClientId: 'rLyOk8QcWm4UCiZTxptmBCzSI3Jsx5cD',
    crossRiverClientSecret: process.env.CROSS_RIVER_CLIENT_SECRET ?? '',

    crossRiverSigningSecret: process.env.CROSS_RIVER_SIGNING_SECRET ?? '',
    crossRiverAuthUrl: process.env.CROSS_RIVER_AUTH_URL ?? '',
    crossRiverCoreUrl: 'https://sandbox.crbcos.com/CORE/v1',
    crossRiverCoreAchUrl: 'https://sandbox.crbcos.com/ach/v1',
    crossRiverWebhookUrl: 'https://sandbox.crbcos.com/webhooks/v1',

    useProxy: 'false',
    proxyProtocol: 'http',
    proxyHost: '************',
    proxyPort: 4128,
    proxyUserName: process.env.PROXY_USERNAME ?? '',
    proxyPassword: process.env.PROXY_PASSWORD ?? '',

    crEnabled: process.env.CR_ENABLED ?? 'true',
    crPartnerWhiteList: '[]',
    useMockAchReceiver: 'false',
  },
  taktile: {
    webhookApiKey: process.env.TAKTILE_INBOUND_API_KEY ?? '',
  },
  ticketingService: {
    baseUrl: process.env.FRESHDESK_BASE_URL ?? 'https://forward-sandbox.freshdesk.com',
    apiKey: process.env.FRESHDESK_API_KEY ?? '',
    freshdeskEnabled: 'true',
    freshdeskEnvironment: 'Test',
  },
  retoolUrl: 'https://retool.dev.getfwd.com',
  accountServiceUrl: 'http://account-service.dev.getfwd.net',
  boardingServiceUrl: 'http://boarding-service.dev.getfwd.net',
  decisioningServiceUrl: 'http://decisioning-service.dev.getfwd.net',
  notificationsServiceUrl: 'https://notifications-service.dev.getfwd.net',
  paymentServiceUrl: 'http://payments-core-service.dev.getfwd.net',
  fileServiceUrl: 'http://file-service.dev.getfwd.net',
  clientId: process.env.CLIENT_ID ?? '********-0000-0000-0001-************',
  clientSecret: process.env.CLIENT_SECRET ?? '',
  blindIndexEncryptionSecretName: process.env.BLIND_INDEX_ENCRYPTION_SECRET_NAME ?? 'BLIND_INDEX_ENCRYPTION_SECRET',
  blindIndexEncryptionSalt: process.env.BLIND_INDEX_ENCRYPTION_SALT ?? '',
  fusionAuthUrl: 'https://auth.dev.getfwd.com',
  microserviceMasterEntityId: '********-0000-0000-0000-************',
  tenantIdList: `{"${FWD_TENANT_ID}":"https://portal.dev.getfwd.com","${FISERV_TENANT_ID}":"https://qaportal.dev.getfwd.com"}`,
  sentryDsn: 'https://<EMAIL>/****************',
  coreResources: {
    CRPullS3Bucket: {
      Type: 'AWS::S3::Bucket',
      Properties: {
        BucketName: 'fwd-sftp-pull-development-cr',
      },
    },
  },
  settlementResources: {},
  Resources: {},
  riskServiceFusionAuth: {
    clientId: '********-0000-0000-0001-000000000011',
    clientSecret: process.env.RISK_SERVICE_CLIENT_SECRET ?? '',
  },
  vault: {
    vpcEndpoint: {
      endpointType: 'PRIVATE',
      vpcEndpointIds: ['vpce-081a8293801cb184a'],
    },
    vpc: {
      // id: 'vpc-0ef8e78d2e56f29b3',
      securityGroupIds: ['sg-034798982de5a8092'],
      subnetIds: ['subnet-0dd3156d87da9820c', 'subnet-04e01ec4f55300336', 'subnet-0d945cf4d5f8ad548'],
    },
    // kmsKeyId: 'arn:aws:kms:us-east-1:************:key/68d730a3-a534-456e-bcc9-b3cd438e4b08',
    kmsKeyId: 'alias/vault-key',
    kmsKeyVersion: '1',
    resources: {
      Resources: {},
    },
    tenantId: 'vtnt_2f2lb14JFArkVDdarv9646DmrSt',
    clientId: '********-0000-0000-0001-000000000007',
    clientSecret: process.env.CLIENT_SECRET ?? '',
    apiUrl: 'https://2ibeiligvf.execute-api.us-east-1.amazonaws.com/development',
  },
  warehouse: {
    username: 'analytics_event_user',
    password: process.env.WAREHOUSE_DB_PASSWORD ?? '',
    host: 'aurora-postgresql-development.cluster-ro-clo2o8acmq1x.us-east-1.rds.amazonaws.com',
    writeHost: 'aurora-postgresql-development.cluster-clo2o8acmq1x.us-east-1.rds.amazonaws.com',
    database: 'warehouse',
  },
};
