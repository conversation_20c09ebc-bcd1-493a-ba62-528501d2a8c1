// This the Default Environment when a stage is not specified

import { PlaidEnvironments } from 'plaid';

import type { Environment } from './environment.types.js';
const AWS_ACCOUNT_ID = '************';

export const env: Environment = {
  name: 'development',
  region: 'us-east-1',
  accountId: '************',
  otelExporter: 'none',
  deploymentRole: `arn:aws:iam::${AWS_ACCOUNT_ID}:role/sls-cf-executor-role`,
  plaid: {
    plaidEnv: PlaidEnvironments.sandbox,
    tenantPlaidConfigData: process.env.TENANT_PLAID_CONFIG_DATA ?? '{}',
    webhookTopicName: 'development-us-east-1-plaid-webhook-received',
  },
  decisioning: {
    slack: {
      boardingErrorWebhookUrl: '',
      dailyMonitoringWebhookUrl: '',
    },
    boarding: {
      snsErrors: {
        arn: '',
      },
    },
  },
  dynamo: {
    endpoint: 'http://127.0.0.1:4566',
    eventsTableName: `EVENTS_TABLE`,
    paymentsCoreTableName: 'INITIAL_TABLE',
    riskServiceTableName: 'RISK_SERVICE_TABLE',
    reportsTableName: 'reports',
    vaultTableName: 'vault_table',
  },
  residualsService: {
    databaseUrl: process.env.RESIDUALS_DATABASE_URL ?? '',
    vpcEndpoint: {
      endpointType: 'PRIVATE',
      vpcEndpointIds: ['vpce-081a8293801cb184a'],
    },
    vpc: {
      id: 'vpc-0ef8e78d2e56f29b3',
      securityGroupIds: ['sg-034798982de5a8092'],
      subnetIds: ['subnet-0dd3156d87da9820c', 'subnet-04e01ec4f55300336', 'subnet-0d945cf4d5f8ad548'],
    },
  },
  flagsmith: {
    environmentId: process.env.FLAGSMITH_ENVIRONMENT_ID ?? '',
    apiUrl: process.env.FLAGSMITH_API_URL ?? 'https://flags.getfwd.com/api/v1/',
  },
  fiserv: {
    marketplaceApiUrl: 'https://connect.uat.fiservapis.com/unified',
  },
  customDomains: { core: [], decisioning: [], settlement: [], notifications: [] },
  sftp: {
    awsEndpoint: process.env.AWS_ENDPOINT ?? '',
    pushRetrySchedule: 'rate(1 hour)',
    configurations: [
      {
        id: 'devCrossRiver',
        pull: {
          name: 'crossRiver',
          bucket: 'fwd-sftp-dev-cr',
          password: process.env.CROSS_RIVER_SFTP_PASSWORD,
          AwsSecretsPrivateKeyName: process.env.CROSS_RIVER_SFTP_SSH_KEY_NAME,
          host: 'p2022-zdbeb3c87-zec68d5ba-gtw.zbe25656a.scsi.sh',
          port: 2022,
          username: 'dimassh',
          paths: [
            {
              source_path: '/sftp',
              destination_path: 'forward',
            },
          ],
          schedule: 'rate(1 hour)',
        },
        push: {
          name: 'crossRiver',
          bucket: 'fwd-sftp-dev-cr',
          password: process.env.CROSS_RIVER_SFTP_PASSWORD,
          AwsSecretsPrivateKeyName: process.env.CROSS_RIVER_SFTP_SSH_KEY_NAME,
          host: 'p2022-zdbeb3c87-zec68d5ba-gtw.zbe25656a.scsi.sh',
          port: 2022,
          username: 'dimassh',
          useExistingBucket: false,
          paths: [
            {
              sftp_root: '/sftp',
              source_path: '/sftp',
              destination_path: 'forward',
            },
          ],
        },
      },
    ],
  },

  sns: {
    accountCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_created`,
    },
    accountUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:production-us-east-1-merchant_updated`,
    },
    partnerCreation: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-partner_created`,
    },
    applicationStatuses: {
      canceled: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_boarding_cancelled`,
      },
      approved: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_boarding_approved`,
      },
      manuallyApproved: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_boarding_manually_approved`,
      },
      manuallyDeclined: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_boarding_manually_declined`,
      },
      statusChanged: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_boarding_status_changed`,
      },
      updated: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_boarding_updated`,
      },
      submitted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_boarding_submitted`,
      },
      needInformation: {
        arn: '',
      },
      deleted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_boarding_deleted`,
      },
      payeeApplicationSubmitted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-boarding_payee_application_submitted`,
      },
      payeeApplicationUnderReview: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-boarding_payee_application_under_review`,
      },
    },
    disputeCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-disputeCreated`,
    },
    disputeAccepted: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-disputeAccepted`,
    },
    disputeContested: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-disputeContested`,
    },
    disputeUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-disputeUpdated`,
    },
    merchantPayoutsDisabled: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_payouts_disabled`,
    },
    merchantPayoutsEnabled: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_payouts_enabled`,
    },
    bankAccountValidated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-bank_account_validated`,
    },
    bankAccountVerified: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-bank_account_verified`,
    },
    contractorCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-contractor_created`,
    },
    contractorActivated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-contractor_activated`,
    },
    contractorDeactivated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-contractor_deactivated`,
    },
    allEventsTopic: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-all-events-topic`,
    },
    paymentGatewaySettingsUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-merchant_gateway_updated`,
    },
    analyticsRequestCreatedTopic: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-analytics_request_created`,
    },
    terminalIdCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:dev-us-east-1-terminal_id_created`,
    },
  },
  sqs: {
    crossRiverWebhookEvents: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-cross-river-webhook-queue`,
    },
    decisionInbound: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:decisioning-all-events`,
    },
    grailpayWebhookEvents: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-grailpay-queue`,
    },
    releaseGrossFundingHolds: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-release-gross-funding-holds`,
      url: `https://sqs.us-east-1.amazonaws.com/${AWS_ACCOUNT_ID}/development-us-east-1-release-gross-funding-holds`,
    },
    grailpayWebhookEventsQueueUrl: `https://sqs.us-east-1.amazonaws.com/${AWS_ACCOUNT_ID}/serverless-core-grailpay-webhook-queue`,
    taktileQueueArn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-taktile-webhook-received`,
    notificationDeliveryCreatedArn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:development-us-east-1-notification-delivery-created`,
  },
  svix: {
    svixApiKey: process.env.SVIX_API_KEY ?? '',
    svixEnabled: 'false',
  },
  settlement: {
    tenants: [
      {
        name: 'fiserv',
        id: 'tnt_2Pg1igVV56xMrBU4AhLQdecy2tP',
      },
      { name: 'dbd', id: 'tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG' },
    ],
    snowflake: {
      account: 'kfb28717',
      username: process.env.SNOWFLAKE_SERVICE_USER ?? '',
      privateKey: process.env.SNOWFLAKE_SERVICE_KEY ?? '',
      database: 'dbd_dev',
      schema: 'data',
      application: 'settlement-dev',
      warehouse: 'compute_wh',
      role: 'app_service_role',
    },
    s3: {
      bucketName: 'dbdventures-sf-stage-dev',
      useExistingBucket: false,
    },
    slack: {
      webhookUrl: process.env.SLACK_SETTLEMENT_WEBHOOK_URL ?? '',
    },
    sns: { topicName: 'settlement-failed' },
  },
  settlementQueue: {
    stateMachineArn: `arn:aws:states:us-east-1:${AWS_ACCOUNT_ID}:stateMachine:settlementSettleQueueProcess-development`,
  },
  settlementPayout: {
    stateMachineArn: `arn:aws:states:us-east-1:${AWS_ACCOUNT_ID}:stateMachine:settlementPayoutProcess-development`,
    tenantACHData: process.env.TENANT_ACH_DATA ?? '{}',
    tenantOriginatorData: process.env.TENANT_ORIGINATOR_DATA ?? '{}',
    vaultApiKey: process.env.VAULT_API_KEY ?? '',
    parallelProcessBatchSize: process.env.PARALLEL_PROCESS_BATCH_SIZE ?? '10',
  },
  crossRiver: {
    crossRiverTenantMasterAccountNumbers:
      '{"tnt_2Pg1igVV56xMrBU4AhLQdecy2tP":"**********","tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG":"**********"}',
    crossRiverTenantSettlementAccountNumbers:
      '{"tnt_2Pg1igVV56xMrBU4AhLQdecy2tP":"************","tnt_2MvQt8U36cbOXKPgdB6qOZjoWtG":"************"}',
    crossRiverTenantAchSettlementAccountNumbers:
      '{“tnt_2MvQjC8bzx3RLPT6si00auqLOjU”: "************", "tnt_2MvQlZSIR823Wh0NXPRjCUMiorb":"************"}',
    crossRiverPartnerId: '826fa648-5cb1-4ddd-a12f-af4e01146b1d',
    crossRiverTenantSettlementSubledgerAccountNumbers: '{}',
    crossRiverClientId: 'rLyOk8QcWm4UCiZTxptmBCzSI3Jsx5cD',
    crossRiverClientSecret: process.env.CROSS_RIVER_CLIENT_SECRET ?? '',
    crossRiverSigningSecret: process.env.CROSS_RIVER_SIGNING_SECRET ?? '',
    crossRiverAuthUrl: process.env.CROSS_RIVER_AUTH_URL ?? '',
    crossRiverCoreUrl: 'https://sandbox.crbcos.com/CORE/v1',
    crossRiverCoreAchUrl: 'https://sandbox.crbcos.com/ach/v1',
    crossRiverWebhookUrl: 'https://sandbox.crbcos.com/webhooks/v1',

    useProxy: 'true',
    proxyProtocol: 'http',
    proxyHost: '************',
    proxyPort: 4128,
    proxyUserName: process.env.PROXY_USERNAME ?? '',
    proxyPassword: process.env.PROXY_PASSWORD ?? '',

    crEnabled: 'true',
    crPartnerWhiteList: '[]',
    useMockAchReceiver: 'true',
  },
  taktile: {
    webhookApiKey: process.env.TAKTILE_INBOUND_API_KEY ?? '',
  },
  ticketingService: {
    baseUrl: 'https://forward.freshdesk.com',
    apiKey: process.env.FRESHDESK_API_KEY ?? '',
    freshdeskEnabled: 'true',
    freshdeskEnvironment: 'Test',
  },
  retoolUrl: 'https://retool.getfwd.com',
  accountServiceUrl: 'https://account-service.getfwd.dev',
  boardingServiceUrl: 'https://boarding-service.getfwd.dev',
  decisioningServiceUrl: 'https://decisioning-service.getfwd.dev',
  notificationsServiceUrl: 'https://notifications-service.getfwd.dev',
  paymentServiceUrl: 'https://payments-core-service.getfwd.dev',
  fileServiceUrl: 'https://account-service.getfwd.dev',
  clientId: '********-0000-0000-0001-************',
  clientSecret: process.env.CLIENT_SECRET ?? '',
  fusionAuthUrl: process.env.FUSIONAUTH_URL ?? 'https://auth.getfwd.dev',
  fusionAuthDomain: process.env.AUTH_DOMAIN ?? 'https://auth.dbd-dev.com',
  fusionAuthIssuerDomain:
    process.env.AUTH_ISSUER_DOMAIN ?? 'https://auth.facilifi.co,https://dbd.com,https://auth.dbdventures.com',
  microserviceMasterEntityId: '********-0000-0000-0000-************',
  tenantIdList: 'TODO',
  Resources: {},
  blindIndexEncryptionSecretName: process.env.BLIND_INDEX_ENCRYPTION_SECRET_NAME ?? '',
  blindIndexEncryptionSalt: process.env.BLIND_INDEX_ENCRYPTION_SALT ?? '',
  coreResources: {},
  settlementResources: {},
  riskServiceFusionAuth: {
    clientId: '********-0000-0000-0001-************',
    clientSecret: process.env.RISK_SERVICE_CLIENT_SECRET ?? '',
  },
  vault: {
    vpc: {
      id: 'vpc-0cbc903d15542d395',
      securityGroupIds: [],
      subnetIds: [],
    },
    vpcEndpoint: {
      endpointType: 'PRIVATE',
      vpcEndpointIds: ['vpce-081a8293801cb184a'],
    },
    kmsKeyId: 'foo',
    kmsKeyVersion: '1',
    resources: {
      Resources: {},
    },
    tenantId: 'vtnt_forward1',
    clientId: '********-0000-0000-0001-************',
    clientSecret: 'shhh',
    apiUrl: 'https://vault.getfwd.dev',
  },
  sentryDsn: '',
  warehouse: {
    username: 'warehouse',
    password: 'warehouse',
    host: '',
    database: 'warehouse',
  },
  copilotSettings: {
    copilotPassword: process.env.COPILOT_PASSWORD ?? '',
  },
  accountCreatedResources: {
    crossRiverEnabled: 'true',
    defaultCheckoutSettingDomain: 'https://checkout.pci.getfwd.com',
  },
};
