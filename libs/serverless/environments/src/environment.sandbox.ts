import { PlaidEnvironments } from 'plaid';

import type { Environment } from './environment.types.js';
const AWS_ACCOUNT_ID = '************';
const FWD_TENANT_ID = 'tnt_2MvQjC8bzx3RLPT6si00auqLOjU';
const FISERV_TENANT_ID = 'tnt_2MvQlZSIR823Wh0NXPRjCUMiorb';

export const env: Environment = {
  name: 'sandbox',
  profile: '',
  region: 'us-east-1',
  accountId: AWS_ACCOUNT_ID,
  dataDogEnabled: false,
  otelExporter: 'none',
  deploymentRole: `arn:aws:iam::${AWS_ACCOUNT_ID}:role/sls-cf-executor-role`,
  fusionAuthDomain: 'https://auth.sandbox.getfwd.com',
  fusionAuthIssuerDomain: 'https://auth.getfwd.com,https://auth.getfwd.dev',
  vpc: {
    securityGroupIds: ['sg-04d83e9e577e01e64'],
    subnetIds: ['subnet-00c09b8a5717eb3ed', 'subnet-00f5b99edd087de11', 'subnet-086ba5b26e5984eb6'],
  },
  dynamo: {
    eventsTableName: `events-table`,
    paymentsCoreTableName: `payments-core-table`,
    riskServiceTableName: `risk-service-table`,
    reportsTableName: 'reports-table',
    vaultTableName: 'vault-table',
    endpoint: 'https://dynamodb.us-east-1.amazonaws.com',
  },
  residualsService: {
    databaseUrl: process.env.RESIDUALS_DATABASE_URL ?? '',
    vpcEndpoint: {
      endpointType: 'PRIVATE',
      vpcEndpointIds: ['vpce-0b90834dc9d83a616'],
    },
    vpc: {
      id: 'vpc-028bb21d066ce0b6a',
      securityGroupIds: ['sg-056609deb613011ff'],
      subnetIds: ['subnet-086ba5b26e5984eb6', 'subnet-00f5b99edd087de11', 'subnet-00c09b8a5717eb3ed'],
    },
  },
  plaid: {
    plaidEnv: PlaidEnvironments.sandbox,
    tenantPlaidConfigData: process.env.TENANT_PLAID_CONFIG_DATA ?? '{}',
    webhookTopicName: 'sandbox-us-east-1-plaid-webhook-received',
  },
  copilotSettings: {
    copilotPassword: process.env.COPILOT_PASSWORD ?? '',
    copilotTestMerchantId: process.env.COPILOT_TEST_MERCHANT_ID ?? '',
  },
  decisioning: {
    slack: {
      boardingErrorWebhookUrl: process.env.SLACK_BOARDING_ERROR_WEBHOOK_URL ?? '',
      dailyMonitoringWebhookUrl: '',
    },
    boarding: {
      snsErrors: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-boarding_errored`,
      },
    },
  },
  grailpay: {
    forwardBaseUrl: process.env.GRAILPAY_FORWARD_BASE_URL ?? '',
    forwardSecret: process.env.GRAILPAY_FORWARD_SECRET ?? '',
    forwardProcessorSecret: process.env.GRAILPAY_FORWARD_PROCESSOR_SECRET ?? '',
    fiservBaseUrl: process.env.GRAILPAY_FISERV_BASE_URL ?? '',
    fiservSecret: process.env.GRAILPAY_FISERV_SECRET ?? '',
    fiservProcessorSecret: process.env.GRAILPAY_FISERV_PROCESSOR_SECRET ?? '',
    mockGrailpay: true,
    tenantsBankGatewayIds:
      '{"tnt_2MvQjC8bzx3RLPT6si00auqLOjU":"Forward-Grailpay", "tnt_2MvQlZSIR823Wh0NXPRjCUMiorb":"Forward-Grailpay"}',
    tenantVendorMap:
      '{"vendor-id-1":"tnt_2MvQjC8bzx3RLPT6si00auqLOjU", "vendor-id-2":"tnt_2MvQlZSIR823Wh0NXPRjCUMiorb"}',
  },
  fiserv: {
    marketplaceApiUrl: 'https://connect.uat.fiservapis.com/unified',
  },
  flagsmith: {
    environmentId: process.env.FLAGSMITH_ENVIRONMENT_ID ?? '',
    apiUrl: 'https://flags.getfwd.com/api/v1/',
  },
  sns: {
    partnerCreation: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-partner_created`,
    },
    accountCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_created`,
    },
    accountUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_updated`,
    },
    terminalIdCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_terminal_id_created`,
    },
    applicationStatuses: {
      canceled: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_cancelled`,
      },
      approved: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_approved`,
      },
      manuallyApproved: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_manually_approved`,
      },
      manuallyDeclined: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_manually_declined`,
      },
      statusChanged: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_status_changed`,
      },
      updated: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_updated`,
      },
      submitted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_submitted`,
      },
      needInformation: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_need_information`,
      },
      deleted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_boarding_deleted`,
      },
      payeeApplicationSubmitted: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-boarding_payee_application_submitted`,
      },
      payeeApplicationUnderReview: {
        arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-boarding_payee_application_under_review`,
      },
    },
    disputeCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-disputeCreated`,
    },
    disputeAccepted: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-disputeAccepted`,
    },
    disputeContested: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-disputeContested`,
    },
    disputeUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-disputeUpdated`,
    },
    merchantPayoutsDisabled: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_payouts_disabled`,
    },
    merchantPayoutsEnabled: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_payouts_enabled`,
    },
    bankAccountValidated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-bank_account_validated`,
    },
    bankAccountVerified: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-bank_account_verified`,
    },
    contractorCreated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-contractor_created`,
    },
    contractorActivated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-contractor_activated`,
    },
    contractorDeactivated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-contractor_deactivated`,
    },
    allEventsTopic: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-all-events-topic`,
    },
    paymentGatewaySettingsUpdated: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-merchant_gateway_updated`,
    },
    analyticsRequestCreatedTopic: {
      arn: `arn:aws:sns:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-analytics_request_created`,
    },
  },
  sqs: {
    crossRiverWebhookEvents: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-cross-river-webhook-queue`,
    },
    decisionInbound: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:decisioning-all-events`,
    },
    grailpayWebhookEvents: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:serverless-core-grailpay-webhook-queue`,
    },
    releaseGrossFundingHolds: {
      arn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-release-gross-funding-holds`,
      url: `https://sqs.us-east-1.amazonaws.com/${AWS_ACCOUNT_ID}/sandbox-us-east-1-release-gross-funding-holds`,
    },
    grailpayWebhookEventsQueueUrl: `https://sqs.us-east-1.amazonaws.com/${AWS_ACCOUNT_ID}/serverless-core-grailpay-webhook-queue`,
    taktileQueueArn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-taktile-webhook-received`,
    notificationDeliveryCreatedArn: `arn:aws:sqs:us-east-1:${AWS_ACCOUNT_ID}:sandbox-us-east-1-notification-delivery-created`,
  },
  svix: {
    svixApiKey: process.env.SVIX_API_KEY ?? '',
    svixEnabled: 'true',
  },
  customDomains: {
    core: [
      {
        domainName: 'api2.sandbox.getfwd.com',
        basePath: '',
        enabled: true,
      },
    ],
    decisioning: [],
    settlement: [
      {
        domainName: 'api2.sandbox.getfwd.com',
        basePath: '',
        enabled: true,
      },
    ],
    notifications: [
      {
        domainName: 'api2.sandbox.getfwd.com',
        basePath: 'notifications',
        enabled: true,
      },
    ],
  },
  settlement: {
    tenants: [
      {
        name: 'fiserv',
        id: FISERV_TENANT_ID,
        email: '<EMAIL>',
      },
      { name: 'dbd', id: FWD_TENANT_ID, email: '<EMAIL>' },
    ],
    snowflake: {
      account: 'kfb28717',
      username: process.env.SNOWFLAKE_SERVICE_USER ?? '',
      privateKey: process.env.SNOWFLAKE_SERVICE_KEY ?? '',
      database: 'fwd_sandbox',
      schema: 'data',
      application: 'settlement-sandbox',
      warehouse: 'compute_wh',
      role: 'app_service_role',
    },
    s3: {
      bucketName: 'dbdventures-sf-stage-sandbox', // TODO: change to sandbox bucket
      useExistingBucket: true,
    },
    slack: {
      webhookUrl: process.env.SLACK_SETTLEMENT_WEBHOOK_URL ?? '',
    },
    sns: { topicName: 'settlement-failed' },
  },
  sftp: {
    awsEndpoint: process.env.AWS_ENDPOINT ?? '',
    pushRetrySchedule: 'rate(12 hours)', //Update when we have configs
    configurations: [],
  },
  settlementQueue: {
    stateMachineArn: `arn:aws:states:us-east-1:${AWS_ACCOUNT_ID}:stateMachine:settlementSettleQueueProcess-sandbox`,
  },
  accountCreatedResources: {
    defaultMockGatewayName: 'mockGateway',
    crossRiverEnabled: 'true',
    defaultCheckoutSettingDomain: 'https://checkout-sandbox.pci.getfwd.co',
  },
  settlementPayout: {
    stateMachineArn: `arn:aws:states:us-east-1:${AWS_ACCOUNT_ID}:stateMachine:settlementPayoutProcess-sandbox`,
    tenantACHData: process.env.TENANT_ACH_DATA ?? '{}',
    tenantOriginatorData: process.env.TENANT_ORIGINATOR_DATA ?? '{}',
    vaultApiKey: process.env.VAULT_API_KEY ?? '',
    parallelProcessBatchSize: process.env.PARALLEL_PROCESS_BATCH_SIZE ?? '10',
  },
  crossRiver: {
    crossRiverTenantMasterAccountNumbers: process.env.CROSS_RIVER_TENANT_MASTER_ACCOUNT_NUMBERS ?? '',
    crossRiverTenantSettlementAccountNumbers: process.env.CROSS_RIVER_TENANT_SETTLEMENT_ACCOUNT_NUMBERS ?? '',
    crossRiverTenantAchSettlementAccountNumbers: process.env.CROSS_RIVER_TENANT_ACH_SETTLEMENT_ACCOUNT_NUMBERS ?? '',
    crossRiverPartnerId: '826fa648-5cb1-4ddd-a12f-af4e01146b1d',
    crossRiverClientId: 'rLyOk8QcWm4UCiZTxptmBCzSI3Jsx5cD',
    crossRiverClientSecret: process.env.CROSS_RIVER_CLIENT_SECRET ?? '',
    crossRiverSigningSecret: process.env.CROSS_RIVER_SIGNING_SECRET ?? '',
    crossRiverAuthUrl: process.env.CROSS_RIVER_AUTH_URL ?? '',
    crossRiverCoreUrl: 'https://sandbox.crbcos.com/CORE/v1',
    crossRiverCoreAchUrl: 'https://sandbox.crbcos.com/ach/v1',
    crossRiverWebhookUrl: 'https://sandbox.crbcos.com/webhooks/v1',

    useProxy: 'false',
    crEnabled: 'true',
    crPartnerWhiteList: '[]',
    useMockAchReceiver: 'false',
  },
  taktile: {
    webhookApiKey: process.env.TAKTILE_INBOUND_API_KEY ?? '',
  },
  ticketingService: {
    baseUrl: process.env.FRESHDESK_BASE_URL ?? 'https://forward-sandbox.freshdesk.com',
    apiKey: process.env.FRESHDESK_API_KEY ?? '',
    freshdeskEnabled: 'true',
    freshdeskEnvironment: 'Test',
  },
  retoolUrl: 'https://retool.sandbox.getfwd.com',
  accountServiceUrl: 'http://account-service.sandbox.getfwd.net',
  boardingServiceUrl: 'http://boarding-service.sandbox.getfwd.net',
  decisioningServiceUrl: 'http://decisioning-service.sandbox.getfwd.net',
  notificationsServiceUrl: 'https://notifications-service.sandbox.getfwd.net',
  paymentServiceUrl: 'http://payments-core-service.sandbox.getfwd.net',
  fileServiceUrl: 'http://file-service.sandbox.getfwd.net',
  clientId: '********-0000-0000-0001-************',
  clientSecret: process.env.CLIENT_SECRET ?? '',
  fusionAuthUrl: 'https://auth.sandbox.getfwd.com',
  microserviceMasterEntityId: '********-0000-0000-0000-************',
  tenantIdList: `{"${FWD_TENANT_ID}":"https://portal.sandbox.getfwd.com","${FISERV_TENANT_ID}":"https://fiserv.sandbox.getfwd.com"}`,
  Resources: {},
  blindIndexEncryptionSecretName: process.env.BLIND_INDEX_ENCRYPTION_SECRET_NAME ?? '',
  blindIndexEncryptionSalt: process.env.BLIND_INDEX_ENCRYPTION_SALT ?? '',
  sentryDsn: 'https://<EMAIL>/****************',
  coreResources: {},
  settlementResources: {},
  riskServiceFusionAuth: {
    clientId: '********-0000-0000-0001-000000000011',
    clientSecret: process.env.RISK_SERVICE_CLIENT_SECRET ?? '',
  },
  vault: {
    vpcEndpoint: {
      endpointType: 'PRIVATE',
      vpcEndpointIds: ['vpce-0b90834dc9d83a616'],
    },
    vpc: {
      // id: 'vpc-028bb21d066ce0b6a',
      securityGroupIds: ['sg-056609deb613011ff'],
      subnetIds: ['subnet-086ba5b26e5984eb6', 'subnet-00f5b99edd087de11', 'subnet-00c09b8a5717eb3ed'],
    },
    kmsKeyId: 'alias/vault-key',
    kmsKeyVersion: '1',
    resources: {
      Resources: {},
    },
    tenantId: 'vtnt_2gClwmmX9ysTOQ9LTEYmKgeASMh',
    clientId: '********-0000-0000-0001-000000000007',
    clientSecret: process.env.CLIENT_SECRET ?? '',
    apiUrl: 'https://57zzwk0sb6.execute-api.us-east-1.amazonaws.com/sandbox',
  },
  warehouse: {
    username: 'analytics_event_user',
    password: process.env.WAREHOUSE_DB_PASSWORD ?? '',
    host: 'aurora-postgresql-customer-sandbox.cluster-ro-ctc4zym5jgbz.us-east-1.rds.amazonaws.com',
    writeHost: 'aurora-postgresql-customer-sandbox.cluster-ctc4zym5jgbz.us-east-1.rds.amazonaws.com',
    database: 'warehouse',
  },
};
