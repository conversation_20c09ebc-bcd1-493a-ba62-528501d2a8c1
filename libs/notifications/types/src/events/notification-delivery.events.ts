import { z } from 'zod';

import { NotificationDeliverySchema } from '../notification-delivery.schema.js';
import { CloudEventSchema } from './cloud-event.schema.js';
import { EventType } from './cloud-event.schema.js';

export const NotificationDeliveryCreatedEventSchema = CloudEventSchema.extend({
  type: z.literal(EventType.NOTIFICATION_DELIVERY_CREATED),
  data: z.object({
    object: NotificationDeliverySchema.extend({
      entity: z.literal('notification_delivery'),
    }),
  }),
});

export const NotificationDeliveryUpdatedEventSchema = CloudEventSchema.extend({
  type: z.literal(EventType.NOTIFICATION_DELIVERY_SENT),
  data: z.object({
    object: NotificationDeliverySchema.extend({
      entity: z.literal('notification_delivery'),
    }),
  }),
});
