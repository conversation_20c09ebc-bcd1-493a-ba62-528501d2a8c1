/*
   The notifcation is what is create by the system.
   We need to know the type of the notfication such at document_request, transaction_held, payouts_disabled.
   Notification belong to something, such as an account, a business, an application

   Notification can be read, this would be in a portal.
   Nofification can have a template which would be use to send the notification via email, sms, push, etc.

   ? Notifcations can he managed by the partner which would mean no deliveries just events sent.
   All notifications will be in the portal regardles of this setting.

*/
import { z } from 'zod';

import { NotificationCategorySchema, NotificationTypeSchema, TimestampSchema } from './common.js';
import { NotificationDeliverySchema } from './notification-delivery.schema.js';

export const NotificationReadStatusSchema = z.enum(['unread', 'read']);

export const OwnerTypeSchema = z.enum(['account', 'business', 'application']);

export type OwnerType = z.infer<typeof OwnerTypeSchema>;

export const NotificationDeliveryStatus = {
  PENDING: 'pending',
  DELIVERED: 'delivered',
  FAILED: 'failed',
  PARTNER_MANAGED: 'partner_managed',
  SKIPPED: 'skipped',
} as const;

export type NotificationDeliveryStatus = (typeof NotificationDeliveryStatus)[keyof typeof NotificationDeliveryStatus];

export const NotificationDeliveryStatusSchema = z.nativeEnum(NotificationDeliveryStatus);

// Base notification schemas
const BaseNotificationSchema = z.object({
  id: z.string(),
  notificationType: NotificationTypeSchema,
  category: NotificationCategorySchema,
  readStatus: NotificationReadStatusSchema,
  ownerId: z.string(),
  ownerType: OwnerTypeSchema,
  businessId: z.string().startsWith('bus_').optional(),
  subject: z.string().max(250),
  content: z.string().max(1000),
  templateId: z.string().optional(), // Foreign key to either PartnerTemplate or NotificationTemplate, on create this will be set?
  templateType: z.enum(['partner', 'system']).optional(), // "partner" or "system" to distinguish template source
  // scheduledFor: TimestampSchema.optional(), // When to send, null for immediate
  expiresAt: TimestampSchema.optional(), // The date and time the notification becomes irrelevant (optional)
  deliveryStatus: NotificationDeliveryStatusSchema,
  data: z.record(z.string(), z.any()),
  partnerHandled: z.boolean(), // Indicates if this notification is handled by the partner
  externalMessageId: z.string().max(50).optional(),
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema,
});

export type BaseNotification = z.infer<typeof BaseNotificationSchema>;

export const BaseCreateNotificationSchema = BaseNotificationSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  readStatus: true,
  templateId: true,
  businessId: true,
  templateType: true,
  deliveryStatus: true,
  partnerHandled: true,
});

export type BaseCreateNotificationSchema = z.infer<typeof BaseCreateNotificationSchema>;

export const NotificationSchema = BaseNotificationSchema;
export type NotificationSchema = z.infer<typeof NotificationSchema>;

export const CreateNotificationSchema = BaseCreateNotificationSchema;

export const UpdateNotificationSchema = z.object({
  readStatus: NotificationReadStatusSchema.optional(),
  deliveryStatus: NotificationDeliveryStatusSchema.optional(),
});

export type UpdateNotificationSchema = z.infer<typeof UpdateNotificationSchema>;
export type CreateNotificationSchema = z.infer<typeof CreateNotificationSchema>;

export const NotificationWithDeliveriesSchema = NotificationSchema.extend({
  deliveries: z.array(NotificationDeliverySchema).optional(),
});
export type NotificationWithDeliveriesSchema = z.infer<typeof NotificationWithDeliveriesSchema>;

// Examples
export const DocumentRequestNotificationExample: NotificationSchema = {
  id: 'notif_1234567890abcdefghijklmnop',
  notificationType: 'document_request',
  category: 'transactional',
  readStatus: 'unread',
  ownerId: '1',
  ownerType: 'account',
  subject: 'Document Request',
  content: 'Please upload your document',
  templateId: '1',
  templateType: 'system',
  deliveryStatus: 'pending',
  partnerHandled: false,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  data: {
    documentId: '1',
    documentType: 'document',
    documentUrl: 'https://example.com/document.pdf',
    nested1: {
      foo: 'bar',
    },
  },
};
