/* Notification Delivery Schema ----------------------------------------

This schema is used to track the delivery of a notification to a specific channel (e.g. email)
It will be created when a notification is sent to a specific channel.

*/
import { z } from 'zod';

import { JsonSchema, TimestampSchema } from './common.js';

export const DeliveryChannelEnum = z.enum(['email']);
export const DeliveryStatusEnum = z.enum(['pending', 'sent', 'failed']);

// Email address schema for validation
const EmailAddressSchema = z.string().email();

// Email channel properties schema
const EmailChannelPropertiesSchema = z
  .object({
    to: z.array(EmailAddressSchema).optional(),
    cc: z.array(EmailAddressSchema).optional(),
    bcc: z.array(EmailAddressSchema).optional(),
  })
  .optional();

export const NotificationDeliverySchema = z.object({
  id: z.string(),
  notificationId: z.string(),
  channel: DeliveryChannelEnum,
  status: DeliveryStatusEnum,
  sentAt: TimestampSchema.optional(),
  failedAt: TimestampSchema.optional(),
  failureReason: z.string().max(1000).optional(),
  failureCount: z.number().optional(),
  errorMessage: z.string().max(1000).optional(),
  metadata: JsonSchema.optional(), // Channel-specific metadata (message ID, etc.)
  configurationId: z.string(), // The delivery configuration ID for the delivery channel
  // Channel-specific properties
  channelProperties: EmailChannelPropertiesSchema, // For email: to, cc, bcc arrays
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema,
});

export type NotificationDelivery = z.infer<typeof NotificationDeliverySchema>;
