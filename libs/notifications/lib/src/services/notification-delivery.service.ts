/* 
This service with process Notification Deliveries
It can fetch them as well as process them which is the process of sending the notification.

*/
import { EmailServiceFactory } from '@notifications/email-service';
import { NotificationDeliveryStatus } from '@notifications/types';

import { DynamoDBTransactionManager } from '../dynamodb/dynamodb-transaction-manager.js';
import { FormattedDeliveryConfigurationEntity } from '../entities/delivery-configuration.entity.js';
import { FormattedNotificationEntity } from '../entities/notification.entity.js';
import { FormattedNotificationDeliveryEntity } from '../entities/notification-delivery.entity.js';
import { FormattedNotificationTemplateEntity } from '../entities/notification-template.entity.js';
import { EventFactory } from '../factories/event.factory.js';
import {
  DeliveryConfigurationRepository,
  EventRepository,
  NotificationDeliveryRepository,
  NotificationRepository,
  NotificationTemplateRepository,
} from '../repositories/repository-types.js';

const MAX_FAILURE_COUNT = 5;

export class NotificationDeliveryService {
  private eventRepo: EventRepository;
  private notificationDeliveryRepo: NotificationDeliveryRepository;
  private notificationRepo: NotificationRepository;
  private notificationTemplateRepo: NotificationTemplateRepository;
  private deliveryConfigurationRepo: DeliveryConfigurationRepository;
  private txManager: DynamoDBTransactionManager;

  constructor({
    eventRepo,
    notificationDeliveryRepo,
    notificationRepo,
    notificationTemplateRepo,
    deliveryConfigurationRepo,
    txManager,
  }: {
    eventRepo: EventRepository;
    notificationDeliveryRepo: NotificationDeliveryRepository;
    notificationRepo: NotificationRepository;
    notificationTemplateRepo: NotificationTemplateRepository;
    deliveryConfigurationRepo: DeliveryConfigurationRepository;
    txManager: DynamoDBTransactionManager;
  }) {
    this.eventRepo = eventRepo;
    this.notificationDeliveryRepo = notificationDeliveryRepo;
    this.notificationRepo = notificationRepo;
    this.notificationTemplateRepo = notificationTemplateRepo;
    this.deliveryConfigurationRepo = deliveryConfigurationRepo;
    this.txManager = txManager;
  }

  /**
   * Deliver a notification by delivery id
   * Fetches the delivery, checks if already sent, sends if not, and updates status/events/notification
   * @param id - NotificationDelivery id
   */
  async sendDelivery(id: string): Promise<void> {
    // Fetch the delivery entity
    const delivery = await this.notificationDeliveryRepo.getById(id);
    if (!delivery) {
      throw new Error(`NotificationDelivery not found: ${id}`);
    }

    // Do not send if already sent
    if (delivery.status === 'sent' || delivery.failureCount >= MAX_FAILURE_COUNT) {
      // Reason: Avoid duplicate sends
      return;
    }

    // Get the notification
    const notification = await this.notificationRepo.getById(delivery.notificationId);
    if (!notification) {
      throw new Error('Notification not found');
    }

    // Get the delivery configuration
    const deliveryConfiguration = await this.deliveryConfigurationRepo.getById(delivery.configurationId);
    if (!deliveryConfiguration) {
      throw new Error('Delivery configuration not found');
    }

    // Get the template
    let template: FormattedNotificationTemplateEntity | undefined = undefined;
    if (notification.templateId) {
      template = await this.notificationTemplateRepo.getTemplateById(notification.templateId);
    }

    try {
      // Only support email for now
      if (
        template &&
        delivery.channel === 'email' &&
        deliveryConfiguration.channels?.email?.enabled &&
        template.channels.email
      ) {
        await this.sendEmailDelivery({
          delivery,
          deliveryConfiguration,
          template,
          notification,
        });
      } else {
        throw new Error('Unsupported channel or missing template/config');
      }

      // Update delivery status to sent and notification to indicate sent
      const now = new Date().toISOString();
      const updateDeliveryItem = this.notificationDeliveryRepo.getUpdateTransactWriteItem({
        id: delivery.id,
        notificationId: delivery.notificationId,
        updates: {
          status: 'sent',
          sentAt: now,
        },
      });
      const sentEvent = EventFactory.createEvent({
        type: 'notification_delivery.sent',
        source: 'notification_service',
        subject: delivery.id,
        data: {
          object: {
            entity: 'notification_delivery',
            ...delivery,
            status: 'sent',
            sentAt: now,
          },
        },
      });
      // Update notification deliveryStatus to delivered
      const updateNotificationItem = this.notificationRepo.getUpdateTransactWriteItem({
        id: notification.id,
        ownerId: notification.ownerId,
        updates: {
          deliveryStatus: NotificationDeliveryStatus.DELIVERED,
        },
      });
      const notificationEvent = EventFactory.createEvent({
        type: 'notification.updated',
        source: 'notification_service',
        subject: notification.id,
        data: {
          object: {
            entity: 'notification',
            ...notification,
            deliveryStatus: 'delivered',
          },
        },
      });
      await this.txManager.executeTransaction([
        updateDeliveryItem,
        this.eventRepo.getTransactWriteItem(sentEvent),
        updateNotificationItem,
        this.eventRepo.getTransactWriteItem(notificationEvent),
      ]);
    } catch (error) {
      // Update delivery status to failed
      const now = new Date().toISOString();
      const updateDeliveryItem = this.notificationDeliveryRepo.getUpdateTransactWriteItem({
        id: delivery.id,
        notificationId: delivery.notificationId,
        updates: {
          status: 'failed',
          failedAt: now,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          failureCount: delivery.failureCount + 1,
        },
      });
      const failedEvent = EventFactory.createEvent({
        type: 'notification_delivery.failed',
        source: 'notification_service',
        subject: delivery.id,
        data: {
          object: {
            entity: 'notification_delivery',
            ...delivery,
            status: 'failed',
            failedAt: now,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            failureCount: delivery.failureCount + 1,
          },
        },
      });
      await this.txManager.executeTransaction([updateDeliveryItem, this.eventRepo.getTransactWriteItem(failedEvent)]);
      throw error;
    }
  }

  /*
    Send all deliveries for a specific notification
    with optional email override for testing
    When testing=true, emails are sent but no data updates are made
  */
  async sendNotificationDeliveries({
    notificationId,
    emailOverride,
    testing = false,
  }: {
    notificationId: string;
    emailOverride?: string;
    testing?: boolean;
  }) {
    // Get all deliveries for this notification
    const deliveries = await this.notificationDeliveryRepo.getByNotificationId(notificationId);

    if (!deliveries || deliveries.length === 0) {
      return {
        message: 'No deliveries found for this notification',
        sentCount: 0,
        failedCount: 0,
        results: [],
      };
    }

    const results = [];
    let sentCount = 0;
    let failedCount = 0;

    for (const delivery of deliveries) {
      try {
        // Get the notification
        const notification = await this.notificationRepo.getById(delivery.notificationId);
        if (!notification) {
          throw new Error('Notification not found');
        }

        // Get the delivery configuration
        const deliveryConfiguration = await this.deliveryConfigurationRepo.getById(delivery.configurationId);
        if (!deliveryConfiguration) {
          throw new Error(`Delivery configuration not found: ${delivery.configurationId}`);
        }

        // Get the template if available
        let template: FormattedNotificationTemplateEntity | undefined = undefined;
        if (notification.templateId) {
          template = await this.notificationTemplateRepo.getTemplateById(notification.templateId);
        }

        // Send based on channel
        if (template && delivery.channel === 'email') {
          await this.sendEmailDelivery({
            delivery,
            deliveryConfiguration,
            template,
            emailOverride,
            notification,
          });
        }

        // Update delivery status to sent (skip in testing mode)
        if (!testing) {
          const updateItem = this.notificationDeliveryRepo.getUpdateTransactWriteItem({
            id: delivery.id,
            notificationId: delivery.notificationId,
            updates: {
              status: 'sent',
              sentAt: new Date().toISOString(),
            },
          });

          const event = EventFactory.createEvent({
            type: 'notification_delivery.sent',
            source: 'notification_service',
            subject: delivery.id,
            data: {
              object: {
                entity: 'notification_delivery',
                ...delivery,
                status: 'sent',
                sentAt: new Date().toISOString(),
              },
            },
          });

          await this.txManager.executeTransaction([updateItem, this.eventRepo.getTransactWriteItem(event)]);
        }

        results.push({
          deliveryId: delivery.id,
          status: 'sent' as const,
        });
        sentCount++;
      } catch (error) {
        // Update delivery status to failed (skip in testing mode)
        if (!testing) {
          try {
            const updateItem = this.notificationDeliveryRepo.getUpdateTransactWriteItem({
              id: delivery.id,
              notificationId: delivery.notificationId,
              updates: {
                status: 'failed',
                failedAt: new Date().toISOString(),
                errorMessage: error instanceof Error ? error.message : 'Unknown error',
              },
            });

            const event = EventFactory.createEvent({
              type: 'notification_delivery.failed',
              source: 'notification_service',
              subject: delivery.id,
              data: {
                object: {
                  entity: 'notification_delivery',
                  ...delivery,
                  status: 'failed',
                  failedAt: new Date().toISOString(),
                  errorMessage: error instanceof Error ? error.message : 'Unknown error',
                },
              },
            });

            await this.txManager.executeTransaction([updateItem, this.eventRepo.getTransactWriteItem(event)]);
          } catch (updateError) {
            console.error('Failed to update delivery status:', updateError);
          }
        }

        results.push({
          deliveryId: delivery.id,
          status: 'failed' as const,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        failedCount++;
      }
    }

    return {
      message: `Processed ${deliveries.length} deliveries`,
      sentCount,
      failedCount,
      results,
    };
  }

  /*
    Send email delivery
  */
  private async sendEmailDelivery({
    delivery,
    deliveryConfiguration,
    template,
    notification,
    emailOverride,
  }: {
    delivery: FormattedNotificationDeliveryEntity;
    deliveryConfiguration: FormattedDeliveryConfigurationEntity;
    template?: FormattedNotificationTemplateEntity;
    notification: FormattedNotificationEntity;
    emailOverride?: string;
  }) {
    if (!deliveryConfiguration.channels?.email?.enabled || !template?.channels?.email) {
      throw new Error('Email channel not enabled or template not found');
    }

    const emailConfig = deliveryConfiguration.channels.email;

    // Determine recipient email
    let toEmail = emailOverride;
    if (
      !toEmail &&
      delivery.channelProperties &&
      'to' in delivery.channelProperties &&
      Array.isArray(delivery.channelProperties.to) &&
      delivery.channelProperties.to.length > 0
    ) {
      toEmail = delivery.channelProperties.to[0];
    }
    if (!toEmail) {
      throw new Error('No recipient email address found');
    }

    if (emailConfig?.provider === 'sendgrid' && emailConfig.config?.sendgrid) {
      const sendgridService = EmailServiceFactory.createWithSendGrid({
        apiKey: emailConfig.config.sendgrid.apiKey,
      });

      await sendgridService.sendEmail({
        to: toEmail,
        from: emailConfig.config.sendgrid.fromEmail,
        fromName: emailConfig.config.sendgrid.fromName,
        replyTo: emailConfig.config.sendgrid.replyTo,
        subject: template.channels.email.subject || 'No subject',
        text: template.channels.email.plainTextTemplate,
        html: template.channels.email.htmlTemplate,
        templateId: template.channels.email.externalTemplateId,
        dynamicData: notification.data,
      });
    } else if (emailConfig?.provider === 'smtp' && emailConfig.config?.smtp) {
      const smtpService = EmailServiceFactory.createWithSmtp({
        host: emailConfig.config.smtp.host,
        port: Number(emailConfig.config.smtp.port),
        secure: true,
        auth: {
          user: emailConfig.config.smtp.auth.user,
          pass: emailConfig.config.smtp.auth.pass,
        },
      });

      await smtpService.sendEmail({
        to: toEmail,
        from: emailConfig.config.smtp.fromEmail,
        replyTo: emailConfig.config.smtp.replyTo,
        subject: template.channels.email.subject || 'No subject',
        text: template.channels.email.plainTextTemplate,
        html: template.channels.email.htmlTemplate,
        dynamicData: notification.data,
      });
    } else {
      throw new Error('No email provider configuration found');
    }
  }
}
