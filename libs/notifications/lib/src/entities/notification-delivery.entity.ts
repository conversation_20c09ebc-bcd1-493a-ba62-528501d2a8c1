import { Entity, FormattedItem } from 'dynamodb-toolbox/entity';
import { any } from 'dynamodb-toolbox/schema/any';
import { item } from 'dynamodb-toolbox/schema/item';
import { number } from 'dynamodb-toolbox/schema/number';
import { record } from 'dynamodb-toolbox/schema/record';
import { string } from 'dynamodb-toolbox/schema/string';
import { Table } from 'dynamodb-toolbox/table';

/**
 * Notification Delivery Entity attributes for DynamoDB Toolbox
 * Mirrors the NotificationDelivery schema in types/src/notification-delivery.schema.ts
 */
const attributes = {
  // GSI1: For querying by notificationId
  GSI1PK: string()
    .link((data) => `DELIVERY_CONFIGURATION#${(data as { configurationId: string }).configurationId}`)
    .hidden(),
  GSI1SK: string()
    .link((data) => `NOTIFICATION_DELIVERY#${(data as { id: string }).id}`)
    .hidden(),

  GSI_ID: string()
    .link((data) => `${(data as { id: string }).id}`)
    .hidden(),

  // Attributes
  id: string().key(),
  notificationId: string().savedAs('notification_id').key(),
  channel: string().enum('email').savedAs('channel'),
  status: string().enum('pending', 'sent', 'failed').savedAs('status'),
  sentAt: string().savedAs('sent_at').optional(),
  failedAt: string().savedAs('failed_at').optional(),
  failureReason: string().savedAs('failure_reason').optional(),
  failureCount: number().savedAs('failure_count').default(0),
  errorMessage: string().savedAs('error_message').optional(),
  metadata: record(string(), any()).savedAs('metadata').optional(),
  configurationId: string().savedAs('configuration_id'),
  channelProperties: record(string(), any()).savedAs('channel_properties').optional(),
};

export const NotificationDeliverySchema = item(attributes);

/**
 * Returns a Notification Delivery Entity for the given table
 * @param table - DynamoDB Toolbox Table instance
 * @returns Entity instance for notification delivery
 */
export const getNotificationDeliveryEntity = (
  table: Table<
    { name: 'PK'; type: 'string' },
    { name: 'SK'; type: 'string' },
    {
      GSI1: {
        type: 'global';
        partitionKey: { name: 'GSI1PK'; type: 'string' };
        sortKey: { name: 'GSI1SK'; type: 'string' };
      };
      GSI2: {
        type: 'global';
        partitionKey: { name: 'GSI2PK'; type: 'string' };
        sortKey: { name: 'GSI2SK'; type: 'string' };
      };
      GSI_ID: {
        type: 'global';
        partitionKey: { name: 'id'; type: 'string' };
      };
    },
    'type'
  >,
) => {
  return new Entity({
    name: 'notification_delivery',
    table,
    schema: NotificationDeliverySchema,
    computeKey: (data) => ({
      // Reason: Partition by notification for efficient queries
      PK: `NOTIFICATION#${(data as any).notificationId}#NOTIFICATION_DELIVERY`,
      SK: `NOTIFICATION_DELIVERY#${(data as { id: string }).id}`,
    }),
    timestamps: {
      created: {
        name: 'createdAt',
        savedAs: 'created_at',
      },
      modified: {
        name: 'updatedAt',
        savedAs: 'updated_at',
      },
    },
  });
};

export type NotificationDeliveryEntity = ReturnType<typeof getNotificationDeliveryEntity>;

export type FormattedNotificationDeliveryEntity = FormattedItem<NotificationDeliveryEntity>;
