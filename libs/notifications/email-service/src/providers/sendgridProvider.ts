// src/providers/sendgridProvider.ts

import { getLogger } from '@dbd/utils';
import { MailDataRequired, MailService } from '@sendgrid/mail';
import handlebars from 'handlebars';

import { EmailOptions, EmailProvider, SendGridConfig } from '../interfaces/emailProvider.js';

/**
 * SendGrid implementation of the EmailProvider interface
 */
export class SendGridProvider implements EmailProvider {
  private client: MailService;
  private logger = getLogger('SendGridProvider');

  constructor(config: SendGridConfig) {
    this.client = new MailService();
    this.client.setApiKey(config.apiKey);
  }

  /**
   * Send an email using SendGrid
   * @param options Email configuration options
   * @returns Promise with SendGrid API response
   */
  async sendEmail(options: EmailOptions): Promise<[any, any]> {
    try {
      if (options.templateId) {
        const msg: MailDataRequired = {
          to: options.to,
          from: { email: options.from, name: options.fromName },
          replyTo: options.replyTo,
          cc: options.cc,
          bcc: options.bcc,
          templateId: options.templateId,
          personalizations: [
            {
              to: options.to,
              dynamicTemplateData: {
                ...options.dynamicData,
                subject: options.subject,
              },
              subject: options.subject,
            },
          ],
        };
        return await this.client.send(msg);
      } else {
        // Render text and html with handlebars if dynamicData is provided
        let renderedText = options.text || '';
        let renderedHtml = options.html || '';
        let renderedSubject = options.subject || '';

        if (options.dynamicData) {
          if (options.text) {
            const textTemplate = handlebars.compile(options.text);
            renderedText = textTemplate(options.dynamicData);
          }
          if (options.html) {
            const htmlTemplate = handlebars.compile(options.html);
            renderedHtml = htmlTemplate(options.dynamicData);
          }
          if (options.subject) {
            const subjectTemplate = handlebars.compile(options.subject);
            renderedSubject = subjectTemplate(options.dynamicData);
          }
        }

        const msg: MailDataRequired = {
          to: options.to,
          from: { email: options.from, name: options.fromName },
          subject: renderedSubject,
          text: renderedText,
          html: renderedHtml,
          cc: options.cc,
          bcc: options.bcc,
          replyTo: options.replyTo,
        };

        return await this.client.send(msg);
      }
    } catch (error) {
      this.logger.error('Error sending email with SendGrid:', error);
      if (
        error instanceof Error &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'body' in error.response
      ) {
        this.logger.error('SendGrid error response body:', error.response.body);
      }
      throw error;
    }
  }

  /**
   * Send a simple text email
   * @param to Recipient email
   * @param from Sender email
   * @param subject Email subject
   * @param text Email body as plain text
   * @returns Promise with SendGrid API response
   */
  async sendTextEmail(params: {
    to: string;
    from: string;
    subject: string;
    text: string;
    cc?: string;
    bcc?: string;
  }): Promise<[any, any]> {
    return this.sendEmail({
      to: params.to,
      from: params.from,
      subject: params.subject,
      text: params.text,
      cc: params.cc,
      bcc: params.bcc,
    });
  }

  /**
   /**
    * Send an HTML email
    * @param params Object containing email parameters
    * @param params.to Recipient email
    * @param params.from Sender email
    * @param params.subject Email subject
    * @param params.html Email body as HTML
    * @param params.cc Optional CC recipient
    * @param params.bcc Optional BCC recipient
    * @returns Promise with SendGrid API response
    */
  async sendHtmlEmail(params: {
    to: string;
    from: string;
    subject: string;
    html: string;
    cc?: string;
    bcc?: string;
  }): Promise<any> {
    return this.sendEmail({
      to: params.to,
      from: params.from,
      subject: params.subject,
      html: params.html,
      cc: params.cc,
      bcc: params.bcc,
    });
  }

  async sendTemplateEmail(params: {
    to: string;
    from: string;
    subject: string;
    templateId: string;
    dynamicData: Record<string, any>;
  }): Promise<[any, any]> {
    const msg: MailDataRequired = {
      to: params.to,
      from: params.from,
      subject: params.subject,
      templateId: params.templateId,
      dynamicTemplateData: params.dynamicData,
      personalizations: [
        {
          to: [{ email: params.to }],
          dynamicTemplateData: params.dynamicData,
        },
      ],
    };
    return this.client.send(msg);
  }
}
