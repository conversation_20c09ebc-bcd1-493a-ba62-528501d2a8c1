{"name": "@notifications/email-service", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "dependencies": {"tslib": "2.8.1", "nodemailer": "7.0.3", "handlebars": "4.7.8", "@sendgrid/mail": "8.1.5"}, "devDependencies": {"@dbd/utils": "workspace:*"}, "nx": {"name": "email-service"}}