{"name": "@dbd/fastify-instrumentation", "version": "0.0.1", "private": true, "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "default": "./src/index.ts"}, "./*": ["./src/*", "./src/*.ts", "./src/*/index.ts", "./src/index.ts"], "./fastify/fastify-instrumentation": "./src/fastify/fastify-instrumentation.ts", "./nextjs/*": "./src/nextjs/*.ts", "./package.json": "./package.json"}, "dependencies": {"@opentelemetry/api": "1.9.0", "@opentelemetry/api-logs": "0.56.0", "@opentelemetry/auto-instrumentations-node": "0.54.0", "@opentelemetry/context-async-hooks": "1.26.0", "@opentelemetry/core": "1.29.0", "@opentelemetry/exporter-jaeger": "1.26.0", "@opentelemetry/exporter-logs-otlp-grpc": "0.56.0", "@opentelemetry/exporter-logs-otlp-http": "0.56.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.56.0", "@opentelemetry/exporter-metrics-otlp-http": "0.56.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.56.0", "@opentelemetry/exporter-trace-otlp-http": "0.56.0", "@opentelemetry/instrumentation": "0.56.0", "@opentelemetry/instrumentation-aws-lambda": "0.49.0", "@opentelemetry/instrumentation-aws-sdk": "0.48.0", "@opentelemetry/instrumentation-dns": "0.41.0", "@opentelemetry/instrumentation-grpc": "0.56.0", "@opentelemetry/instrumentation-http": "0.56.0", "@opentelemetry/instrumentation-mysql": "0.44.0", "@opentelemetry/instrumentation-net": "0.42.0", "@opentelemetry/instrumentation-pg": "0.49.0", "@opentelemetry/instrumentation-pino": "0.45.0", "@opentelemetry/instrumentation-undici": "0.9.0", "@opentelemetry/otlp-exporter-base": "0.56.0", "@opentelemetry/propagator-aws-xray": "1.26.0", "@opentelemetry/propagator-b3": "1.29.0", "@opentelemetry/resource-detector-aws": "1.5.2", "@opentelemetry/resources": "1.29.0", "@opentelemetry/sdk-logs": "0.56.0", "@opentelemetry/sdk-metrics": "1.29.0", "@opentelemetry/sdk-node": "0.56.0", "@opentelemetry/sdk-trace-base": "1.29.0", "@opentelemetry/sdk-trace-node": "1.29.0", "@opentelemetry/semantic-conventions": "1.28.0", "@opentelemetry/winston-transport": "0.10.0", "@sentry/aws-serverless": "9.15.0", "@sentry/nextjs": "9.15.0", "@sentry/node": "9.15.0", "@sentry/opentelemetry": "9.15.0", "@sentry/profiling-node": "9.15.0", "@sentry/react": "9.15.0"}, "nx": {"name": "fastify-instrumentation", "sourceRoot": "libs/instrumentation/fastify/src", "projectType": "library", "targets": {"tsc": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/out-tsc"], "options": {"cwd": "libs/instrumentation/fastify", "command": "tsc --build --incremental --emitDeclarationOnly"}}, "build": {"cache": true, "configurations": {"development": {}, "production": {}}, "defaultConfiguration": "production", "executor": "@nx/esbuild:esbuild", "options": {"assets": [], "bundle": true, "esbuildOptions": {"treeShaking": false, "outExtension": {".js": ".cjs"}, "sourcemap": true}, "external": [], "format": ["cjs"], "generatePackageJson": false, "main": "libs/instrumentation/fastify/src/instrumentation.ts", "minify": false, "outputPath": "dist/libs/instrumentation/fastify", "platform": "node", "sourcesContent": false, "thirdParty": true, "tsConfig": "libs/instrumentation/fastify/tsconfig.lib.json"}, "outputs": ["{options.outputPath}"]}}}}