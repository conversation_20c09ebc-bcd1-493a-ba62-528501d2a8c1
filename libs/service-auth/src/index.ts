import { bearerTokenValidation } from '@dbd/utils';
import axios, { InternalAxiosRequestConfig } from 'axios';
import FormData from 'form-data';
import { jwtDecode } from 'jwt-decode';

export class ServerAuthClass {
  readonly clientId: string;
  #clientSecret: string;
  #token = '';

  readonly fusionAuthUrl: string;
  readonly microserviceEntityId: string;

  constructor(clientId: string, clientSecret: string, fusionAuthUrl: string, microserviceEntityId: string) {
    this.clientId = clientId;
    this.#clientSecret = clientSecret;
    this.fusionAuthUrl = fusionAuthUrl;
    this.microserviceEntityId = microserviceEntityId;
    this.#token = '';
  }

  async getServiceJWT() {
    const token = await this.getServiceToken();
    return jwtDecode(token);
  }

  async authRequest(request: InternalAxiosRequestConfig, accessToken?: string) {
    const token = accessToken || (await this.getAccessToken());
    request.headers.set('Authorization', `Bearer ${token}`);
    return request;
  }

  private async getServiceToken() {
    const formData = new FormData();

    formData.append('grant_type', 'client_credentials');
    formData.append('scope', `target-entity:${this.microserviceEntityId}`);

    const response = await axios.post(`${this.fusionAuthUrl}/oauth2/token`, formData, {
      auth: {
        username: this.clientId,
        password: this.#clientSecret,
      },
    });
    return response.data.access_token;
  }

  async getAccessToken() {
    if (bearerTokenValidation(this.#token)) {
      return this.#token;
    }

    this.#token = await this.getServiceToken();
    return this.#token;
  }
}
