import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  GetBusinessBankAccountByIdCommand,
  GetBusinessByIdCommand,
  GetBusinessUserByIdCommand,
  ListBusinessBankAccountsCommand,
  ListBusinessesCommand,
  ListBusinessesMetaCommand,
  ListBusinessUsersCommand,
  StreamBusinessesCommand,
} from '@dbd/reporting-businesses/business.commands';
import { ListBusinessesResponse } from '@dbd/reporting-businesses/business.dto';
import { BusinessRow, BusinessUserRow } from '@dbd/reporting-businesses/business.types.js';
import { getAuthenticatedClient, handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { OrderDirection, PaginatedResult } from '@dbd/zod-types';
import { BankAccountId, ForbiddenError, ListMeta, NotFoundError } from '@dbd/zod-types-common';

import { getBusinessService } from './business.service.js';

const logger = getLogger('business.loaders');

export async function getBusinessById(
  params: Omit<GetBusinessByIdCommand, 'authUser'>,
): Promise<BusinessRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getBusinessService();
    const data = await service.getBusinessById(
      GetBusinessByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting business by id', params });
    throw handleNextError(error);
  }
}

export async function getBusinessBankAccountById(params: Omit<GetBusinessBankAccountByIdCommand, 'authUser'>) {
  const user = await requireUser();

  const service = await getBusinessService();
  const data = await service.getBusinessBankAccountById(
    GetBusinessBankAccountByIdCommand.parse({
      ...params,
      authUser: user,
    }),
  );

  return data;
}

export async function listBusinesses(params: Omit<ListBusinessesCommand, 'authUser'>): Promise<ListBusinessesResponse> {
  try {
    const user = await requireUser();

    const service = await getBusinessService();
    const data = await service.listBusinesses(
      ListBusinessesCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return ListBusinessesResponse.parse(data);
  } catch (error) {
    logger.error({ err: error, message: 'Error listing businesses', params });
    throw handleNextError(error);
  }
}

export async function listBusinessNames(
  params: Omit<ListBusinessesCommand, 'authUser'>,
): Promise<ListBusinessesResponse> {
  try {
    const user = await requireUser();

    const service = await getBusinessService();
    const data = await service.listBusinesses(
      ListBusinessesCommand.parse({
        ...params,
        params: {
          ...params.params,
          fields: ['business_name', 'business_id', 'partner_id'],
          orderBy: [['business_name', OrderDirection.ASC]],
        },
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing business names', params });
    throw handleNextError(error);
  }
}

export async function listBusinessesMeta(params: Omit<ListBusinessesMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getBusinessService();
    const data = await service.listBusinessesMeta(
      ListBusinessesMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing businesses meta', params });
    throw handleNextError(error);
  }
}

export async function streamBusinesses(
  params: Omit<StreamBusinessesCommand, 'authUser'>,
): Promise<AsyncGenerator<BusinessRow[]>> {
  try {
    const user = await requireUser();

    const service = await getBusinessService();
    const data = await service.streamBusinesses(
      StreamBusinessesCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming businesses', params });
    throw handleNextError(error);
  }
}

export async function listBusinessBankAccounts(params: Omit<ListBusinessBankAccountsCommand, 'authUser'>) {
  try {
    const user = await requireUser();

    const service = await getBusinessService();
    const data = await service.listBusinessBankAccounts(
      ListBusinessBankAccountsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing business bank accounts', params });
    throw handleNextError(error);
  }
}

export async function getBusinessUserById(
  params: Omit<GetBusinessUserByIdCommand, 'authUser'>,
): Promise<BusinessUserRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getBusinessService();
    const data = await service.getBusinessUserById(
      GetBusinessUserByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting business user by id', params });
    throw handleNextError(error);
  }
}

export async function listBusinessUsers(
  params: Omit<ListBusinessUsersCommand, 'authUser'>,
): Promise<PaginatedResult<typeof BusinessUserRow>> {
  try {
    const user = await requireUser();

    const service = await getBusinessService();
    const data = await service.listBusinessUsers(
      ListBusinessUsersCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing business users', params });
    throw handleNextError(error);
  }
}

export async function listBusinessBankAccountDocuments({ bankAccountId }: { bankAccountId: BankAccountId }) {
  const { client, user } = await getAuthenticatedClient();
  const bankAccount = await getBusinessBankAccountById({
    params: {
      id: bankAccountId,
      fields: [],
    },
  });

  if (!bankAccount) {
    throw new NotFoundError({ cause: new Error('Bank account not found') });
  }

  if (
    bankAccount.tenant_id !== user.tenant_id ||
    (user.partner_id && user.partner_id !== bankAccount.partner_id) ||
    (user.business_id && user.business_id !== bankAccount.business_id)
  ) {
    throw new ForbiddenError({ cause: new Error('You are not authorized to view this bank account') });
  }

  const files = await client.files.list(
    {
      source_id: bankAccountId,
      limit: 100,
    },
    bankAccount.business_id,
  );

  return files.data;
}
