'use server';
import 'server-only';

import { ReadWriteRoles } from '@dbd/core-types/constants/auth.constants';
import { FileUnderwritingDocumentType } from '@dbd/core-types/constants/file.constants';
import { BankIdentityScope, ParentSourceTypeEnum, UserEntityTypeEnum } from '@dbd/core-types/enums/common';
import { FileBusinessPurposeEnum } from '@dbd/core-types/enums/file.enums';
import { requireUser } from '@dbd/next-sessions/auth';
import { CreateBusinessCommand } from '@dbd/reporting-businesses/business.commands';
import { CreateBusinessUserSchema, EditBusinessUserSchema } from '@dbd/reporting-businesses/business.types.js';
import { getAuthenticatedClient, handleServiceError, withSentryAction } from '@dbd/ui/lib/server';
import { hasGroups, hasRole } from '@dbd/ui/lib/user';
import { addVaultBankAccount } from '@dbd/ui-services/banking/banking.service.js';
import { getLogger } from '@dbd/utils/logging';
import {
  AuthUserSelfSubType,
  BankAccountId,
  BusinessId,
  ForbiddenError,
  NotFoundError,
  ServerError,
  UserId,
  UserStatus,
} from '@dbd/zod-types-common';
import * as Sentry from '@sentry/nextjs';
import { PlaidAccount } from 'react-plaid-link';
import { Readable } from 'stream';
import { z } from 'zod';

import { BusinessBankAccountFormSchema } from '../schemas/business-bank-account-form-schema.js';
import { getBusinessBankAccountById, getBusinessById, getBusinessUserById } from './business.loaders.js';
import { getBusinessService } from './business.service.js';

const logger = getLogger('business.actions');

export const createBusiness = withSentryAction(async function createBusiness(
  params: Omit<CreateBusinessCommand, 'authUser'>,
) {
  try {
    const user = await requireUser();
    const service = await getBusinessService();
    const data = await service.createBusiness(
      CreateBusinessCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    if (!data) {
      throw new Error('Business not created');
    }

    return {
      success: true as const,
      data,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error creating business', params });
    return handleServiceError(e);
  }
});

export const updateBusinessUserStatus = withSentryAction(async function updateBusinessUserStatus(params: {
  userId: UserId;
  status: UserStatus;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const businessUser = await getBusinessUserById({
      params: {
        id: params.userId,
        fields: [],
      },
    });
    if (!businessUser) {
      throw new NotFoundError({
        cause: new Error(`Business user not found: ${params.userId}`),
      });
    }

    if (
      user.partner_id !== businessUser.partner_id ||
      (user.business_id && user.business_id !== businessUser.business_id)
    ) {
      throw new ForbiddenError({
        cause: new Error(
          `User is not allowed to update business user ${params.userId} for business ${businessUser.business_id}`,
        ),
      });
    }

    const isAdmin = hasGroups(user, ['PARTNER_ADMIN', 'BUSINESS_ADMIN']);
    if (!isAdmin) {
      throw new ForbiddenError({
        cause: new Error('User is not an admin'),
      });
    }

    const response = await client.users.updateStatus(businessUser.id, params.status);
    return { success: true as const, data: response };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error updating business user status', params });
    return handleServiceError(e);
  }
});

export const editBusinessUser = withSentryAction(async function editBusinessUser(params: EditBusinessUserSchema) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const businessUser = await getBusinessUserById({
      params: {
        id: params.user_id,
        fields: [],
      },
    });
    if (!businessUser) {
      throw new NotFoundError({
        cause: new Error(`Business user not found: ${params.user_id}`),
      });
    }

    if (
      user.partner_id !== businessUser.partner_id ||
      (user.business_id && user.business_id !== businessUser.business_id)
    ) {
      throw new ForbiddenError({
        cause: new Error(
          `User is not allowed to edit business user ${params.user_id} for business ${businessUser.business_id}`,
        ),
      });
    }

    const isAdmin = hasGroups(user, ['PARTNER_ADMIN', 'BUSINESS_ADMIN']);
    if (!isAdmin) {
      throw new ForbiddenError({
        cause: new Error(
          `User is not allowed to edit business user ${params.user_id} for business ${businessUser.business_id}`,
        ),
      });
    }

    const response = await client.users.update(businessUser.id, {
      type: 'BUSINESS',
      first_name: params.first_name,
      last_name: params.last_name,
      email: params.email,
      group: params.group,
      phone_number: params.phone_number,
    });

    return { success: true as const, data: response };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error editing business user', params });
    return handleServiceError(e);
  }
});

export const createBusinessUser = withSentryAction(async function createBusinessUser(params: CreateBusinessUserSchema) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const isAdmin = hasGroups(user, ['PARTNER_ADMIN', 'BUSINESS_ADMIN']);
    if (!isAdmin) {
      throw new ForbiddenError();
    }

    if (user.business_id && user.business_id !== params.business_id) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to create business user for business ${params.business_id}`),
      });
    }

    const business = await getBusinessById({
      params: {
        id: params.business_id,
        fields: [],
      },
    });
    if (!business) {
      throw new NotFoundError({
        cause: new Error(`Business not found: ${params.business_id}`),
      });
    }

    if (business.partner_id !== user.partner_id || (user.business_id && user.business_id !== business.business_id)) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to create business user for business ${params.business_id}`),
      });
    }

    const response = await client.users.create(
      {
        first_name: params.first_name,
        last_name: params.last_name,
        email: params.email,
        group: params.group,
        type: 'BUSINESS',
        phone_number: params.phone_number,
      },
      {
        businessId: params.business_id,
      },
    );

    return { success: true as const, data: response };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error creating business user', params });
    return handleServiceError(e);
  }
});

export const generateBusinessLinkToken = withSentryAction(async function generateBusinessLinkToken(params: {
  businessId: BusinessId;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const canAddBankAccount =
      user.type === 'BUSINESS' && user.roles.includes(ReadWriteRoles.BUSINESS_BANK_ACCOUNT_WRITE);
    if (!canAddBankAccount || (user.business_id && params.businessId !== user.business_id)) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to generate link token for business ${params.businessId}`),
      });
    }

    const business = await getBusinessById({
      params: {
        id: params.businessId,
        fields: [],
      },
    });
    if (!business) {
      throw new NotFoundError({
        cause: new Error(`Business not found: ${params.businessId}`),
      });
    }

    const linkToken = await client.bankIdentity.linkToken(
      business.business_id,
      UserEntityTypeEnum.BUSINESS,
      undefined,
      false,
    );

    return {
      success: true as const,
      data: linkToken,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error generating business link token', params });
    return handleServiceError(e);
  }
});

export const generateBusinessVerifyLinkToken = withSentryAction(async function generateBusinessVerifyLinkToken(params: {
  businessId: BusinessId;
  bankAccountId: BankAccountId;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const canAddBankAccount =
      user.type === 'BUSINESS' && user.roles.includes(ReadWriteRoles.BUSINESS_BANK_ACCOUNT_WRITE);
    if (!canAddBankAccount || (user.business_id && params.businessId !== user.business_id)) {
      throw new ForbiddenError();
    }

    const [business, bankAccount] = await Promise.all([
      getBusinessById({
        params: {
          id: params.businessId,
          fields: [],
        },
      }),
      getBusinessBankAccountById({
        params: {
          id: params.bankAccountId,
          fields: [],
        },
      }),
    ]);
    if (!business || !bankAccount) {
      throw new NotFoundError();
    }

    const linkToken = await client.bankIdentity.manualVerifyLinkToken(
      business.business_id,
      params.bankAccountId,
      UserEntityTypeEnum.BUSINESS,
    );

    return {
      success: true as const,
      data: linkToken,
    };
  } catch (e) {
    return handleServiceError(e);
  }
});

export type ExchangeBusinessPublicTokenParams = {
  businessId: BusinessId;
  publicToken: string;
  isManual?: boolean;
  account?: PlaidAccount;
};

export const exchangeBusinessPublicToken = withSentryAction(async function exchangeBusinessPublicToken(
  params: ExchangeBusinessPublicTokenParams,
) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const canAddBankAccount =
      user.type === 'BUSINESS' && user.roles.includes(ReadWriteRoles.BUSINESS_BANK_ACCOUNT_WRITE);
    if (!canAddBankAccount || params.businessId !== user.business_id) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to exchange public token for business ${params.businessId}`),
      });
    }

    const business = await getBusinessById({
      params: {
        id: params.businessId,
        fields: [],
      },
    });
    if (!business) {
      throw new NotFoundError({
        cause: new Error(`Business not found: ${params.businessId}`),
      });
    }

    await client.bankIdentity.exchangePublicToken(
      params.publicToken,
      AuthUserSelfSubType.enum.BUSINESS,
      business.business_id,
      params.isManual,
      params.account,
    );

    return {
      success: true as const,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error exchanging business public token', params });
    return handleServiceError(e);
  }
});

export const verifyBusinessManualBankIdentity = withSentryAction(async function verifyBusinessBankIdentity(params: {
  businessId: BusinessId;
  bankAccountId: BankAccountId;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const canAddBankAccount =
      user.type === 'BUSINESS' && user.roles.includes(ReadWriteRoles.BUSINESS_BANK_ACCOUNT_WRITE);
    if (!canAddBankAccount || params.businessId !== user.business_id) {
      throw new ForbiddenError();
    }

    const [business, bankAccount] = await Promise.all([
      getBusinessById({
        params: {
          id: params.businessId,
          fields: [],
        },
      }),
      getBusinessBankAccountById({
        params: {
          id: params.bankAccountId,
          fields: [],
        },
      }),
    ]);
    if (!business || !bankAccount) {
      throw new NotFoundError();
    }

    await client.bankIdentity.manualVerifyBankIdentity(
      business.business_id,
      UserEntityTypeEnum.BUSINESS,
      params.bankAccountId,
    );

    return {
      success: true as const,
    };
  } catch (e) {
    return handleServiceError(e);
  }
});

const UploadBankAccountDocumentsParams = z.object({
  businessId: BusinessId,
  bankAccountId: BankAccountId,
  files: z.array(z.object({ type: z.string(), file: z.instanceof(File) })),
});

export const uploadBankAccountDocuments = withSentryAction(async function uploadBankAccountDocuments(params: FormData) {
  const { user, accessToken } = await getAuthenticatedClient();
  const types = params.getAll('types') as string[] | null;
  const files = params.getAll('files') as File[] | null;
  if (!files || !types) {
    throw new Error('Files and types are required');
  }

  const fileMap = files.map((file, index) => ({ file, type: types[index] as FileUnderwritingDocumentType }));

  const payload = UploadBankAccountDocumentsParams.parse({
    businessId: params.get('businessId') as BusinessId,
    bankAccountId: params.get('bankAccountId') as BankAccountId,
    files:
      fileMap?.map(({ file, type }) => ({
        file,
        type,
      })) ?? [],
  });

  if (user.business_id !== payload.businessId) {
    throw new ForbiddenError();
  }

  const promises = payload.files.map(async ({ file, type }, index) => {
    try {
      const url = `${process.env.FILE_SERVICE_URL}/files`;
      const body = new FormData();
      body.append('file', file);
      body.append('purpose', FileBusinessPurposeEnum.BANK_VALIDATION_DOCUMENT);
      body.append('source_id', payload.bankAccountId);
      body.append('source_type', ParentSourceTypeEnum.BANK_ACCOUNT);
      body.append('owner_id', payload.businessId);
      body.append('document_type', type);

      const response = await fetch(url, {
        method: 'POST',
        body,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error('Failed to upload file', { cause: data || response.statusText });
      }

      return {
        name: file.name,
        index,
        success: true as const,
        data,
      };
    } catch (e) {
      return {
        name: file.name,
        index,
        success: false as const,
        error: (e as Error).message,
      };
    }
  });

  const responses = await Promise.all(promises);
  const success = responses.every((response) => response.success);

  if (success) {
    return {
      success: true as const,
      data: responses.map((response) => response.data),
    };
  } else {
    return {
      success: false as const,
      data: responses.map((response) => ({
        name: response.name,
        index: response.index,
        error: response.error,
      })),
    };
  }
});

export const createBusinessBankAccount = withSentryAction(async function createBusinessBankAccount(data: FormData) {
  try {
    const { user, accessToken } = await getAuthenticatedClient();

    const params = BusinessBankAccountFormSchema.parse({
      accountNumber: data.get('accountNumber') as string,
      routingNumber: data.get('routingNumber') as string,
      accountType: data.get('accountType') as string,
      businessId: data.get('businessId') as BusinessId,
      document: data.getAll('document') as File[],
    });

    if (!hasRole(user, ReadWriteRoles.BUSINESS_BANK_ACCOUNT_WRITE) || user.business_id !== params.businessId) {
      throw new ForbiddenError({ cause: new Error('User does not have permission to add a bank account') });
    }

    if (!process.env.VAULT_API_KEY || !process.env.VAULT_API_URL) {
      throw new ServerError({ cause: new Error('Vault API key or URL is not set') });
    }

    const file = Readable.from(Buffer.from(await params.document[0].arrayBuffer()));
    const accountName = `${params.accountType.toUpperCase()} (${params.accountNumber.slice(-4)})`;

    const response = await addVaultBankAccount({
      vaultApiKey: process.env.VAULT_API_KEY,
      vaultApiUrl: process.env.VAULT_API_URL,
      accountNumber: params.accountNumber,
      routingNumber: params.routingNumber,
      ownerId: params.businessId,
      scope: BankIdentityScope.BUSINESS,
      accountName: accountName,
      accountType: params.accountType,
      file,
      tenantId: user.tenant_id,
      client: 'mp',
      token: accessToken,
    });

    if (response.bankAccount) {
      return { success: true as const, data: void 0 };
    }

    throw new Error('An unknown error occurred', { cause: response });
  } catch (e: unknown) {
    Sentry.captureException(e);
    return {
      success: false as const,
      error: handleServiceError(e).error.body,
    };
  }
});
