{"compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "files": [], "include": [], "references": [{"path": "../../../utils"}, {"path": "../../../core-types"}, {"path": "../forms"}, {"path": "../../../next-sessions"}, {"path": "../common"}, {"path": "../../../zod-types-common"}, {"path": "../../../zod-types"}, {"path": "../../../account-service-client"}, {"path": "../../../reporting/common/server"}, {"path": "../../../reporting/businesses"}, {"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../../tsconfig.base.json"}