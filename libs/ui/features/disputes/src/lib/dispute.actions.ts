'use server';

import { FileDTO } from '@dbd/core-types/dto/file.dto';
import { ParentSourceTypeEnum } from '@dbd/core-types/enums/common';
import { FileBusinessPurposeEnum } from '@dbd/core-types/enums/file.enums';
import { DisputeAttachment } from '@dbd/reporting-disputes/dispute.types.js';
import { getAuthenticatedClient, handleServiceError, withSentryAction } from '@dbd/ui/lib/server';
import { hasGroups } from '@dbd/ui/lib/user';
import { getLogger } from '@dbd/utils';
import { DisputeStatus } from '@dbd/zod-types/enums';
import { BadRequestError, DisputeId, ForbiddenError, NotFoundError, ServerError } from '@dbd/zod-types-common';

import { getDisputeById } from './dispute.loaders.js';

const logger = getLogger('dispute.actions');

export const acceptDisputeAction = withSentryAction(async function acceptDisputeAction(params: {
  disputeId: DisputeId;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();

    const dispute = await getDisputeById({
      params: {
        id: params.disputeId,
        fields: [],
      },
    });
    if (!dispute) {
      throw new NotFoundError({ cause: new Error(`Dispute not found: ${params.disputeId}`) });
    }

    const isAdmin = hasGroups(user, ['BUSINESS_ADMIN']);
    if (dispute.business_id !== user.business_id || !isAdmin) {
      throw new ForbiddenError({ cause: new Error(`User is not allowed to accept dispute ${params.disputeId}`) });
    }

    if (dispute.status !== DisputeStatus.enum.new) {
      throw new BadRequestError({ cause: new Error('Dispute is not in NEW state') });
    }

    const response = await client.disputes.accept(params.disputeId, { accountId: dispute.account_id });
    if (!response || response.status !== DisputeStatus.enum.accepted) {
      throw new BadRequestError({ cause: new Error('Dispute was not accepted') });
    }

    // This is a hack because of the propagation delay between DynamoDB and the warehouse
    const waitForStateChange = async () => {
      for (let i = 0; i < 10; i++) {
        const newDispute = await getDisputeById({
          params: {
            id: params.disputeId,
            fields: [],
          },
        });

        if (newDispute?.status === DisputeStatus.enum.accepted) {
          return newDispute;
        }

        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      throw new ServerError({ cause: new Error('Dispute state did not change after 10 seconds') });
    };

    const newDispute = await waitForStateChange();
    if (!newDispute) {
      throw new ServerError({ cause: new Error('Dispute was not found') });
    }

    return { success: true as const, data: newDispute };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error accepting dispute', params });
    return handleServiceError(e);
  }
});

export const contestDisputeAction = withSentryAction(async function contestDisputeAction(params: {
  disputeId: DisputeId;
  rebuttal: string;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const dispute = await getDisputeById({
      params: {
        id: params.disputeId,
        fields: [],
      },
    });
    if (!dispute) {
      throw new NotFoundError({ cause: new Error(`Dispute not found: ${params.disputeId}`) });
    }

    const isAdmin = hasGroups(user, ['BUSINESS_ADMIN']);
    if (dispute.business_id !== user.business_id || !isAdmin) {
      throw new ForbiddenError({ cause: new Error(`User is not allowed to contest dispute ${params.disputeId}`) });
    }

    if (dispute.status !== DisputeStatus.enum.new) {
      throw new BadRequestError({ cause: new Error('Dispute is not in NEW state') });
    }

    const response = await client.disputes.update(
      params.disputeId,
      {
        status: DisputeStatus.enum.contested,
        dispute_response: params.rebuttal,
      },
      { accountId: dispute.account_id },
    );

    if (!response || response.status !== DisputeStatus.enum.contested) {
      throw new BadRequestError({ cause: new Error('Dispute was not contested') });
    }

    // This is a hack because of the propagation delay between DynamoDB and the warehouse
    const waitForStateChange = async () => {
      for (let i = 0; i < 10; i++) {
        const newDispute = await getDisputeById({
          params: {
            id: params.disputeId,
            fields: [],
          },
        });

        if (newDispute?.status === DisputeStatus.enum.contested) {
          return newDispute;
        }

        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      throw new ServerError({ cause: new Error('Dispute state did not change after 10 seconds') });
    };

    const newDispute = await waitForStateChange();

    if (!newDispute) {
      throw new ServerError({ cause: new Error('Dispute was not found') });
    }

    return { success: true as const, data: newDispute };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error contesting dispute', params });
    return handleServiceError(e);
  }
});

export const addDisputeAttachmentsAction = withSentryAction(async function addDisputeAttachmentsAction(
  params: FormData,
) {
  try {
    const { user, client, accessToken } = await getAuthenticatedClient();

    const files = params.getAll('files') as File[] | null;
    if (!files || files.length === 0) {
      throw BadRequestError.fromError(new Error('Files is required'));
    }

    const disputeId = DisputeId.parse(params.get('disputeId') as DisputeId);
    const dispute = await getDisputeById({
      params: {
        id: disputeId,
        fields: [],
      },
    });
    if (!dispute) {
      throw new NotFoundError();
    }

    const isAdmin = hasGroups(user, ['BUSINESS_ADMIN']);
    if (dispute.business_id !== user.business_id || !isAdmin) {
      throw new ForbiddenError();
    }

    const promises = files.map(async (file, index) => {
      try {
        const url = `${process.env.FILE_SERVICE_URL}/files`;
        const body = new FormData();
        body.append('file', file);
        body.append('purpose', FileBusinessPurposeEnum.DISPUTE_EVIDENCE);
        body.append('source_id', disputeId);
        body.append('source_type', ParentSourceTypeEnum.DISPUTE);
        body.append('owner_id', dispute.account_id);

        const response = await fetch(url, {
          method: 'POST',
          body,
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });

        const data = (await response.json()) as FileDTO;
        if (!response.ok) {
          throw new Error('Failed to upload file', { cause: data || response.statusText });
        }

        return {
          name: file.name,
          id: data.id,
          index,
          success: true as const,
        };
      } catch (e) {
        return {
          name: file.name,
          index,
          success: false as const,
          error: (e as Error).message,
        };
      }
    });

    const responses = await Promise.all(promises);
    const success = responses.every((response) => response.success);
    if (!success) {
      throw new BadRequestError({
        errors: responses.map((response) => ({
          code: '400',
          path: ['files', response.index],
          message: response.error || 'Failed to add attachment',
        })),
      });
    }

    const updatedDispute = await client.disputes.addAttachments(
      disputeId,
      responses.map((response) => ({
        file_id: response.id,
        description: response.name,
      })),
      { accountId: dispute.account_id },
    );

    return {
      success: true as const,
      data: (updatedDispute.attachments ?? []).map((a) => DisputeAttachment.parse(a)),
    };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error adding dispute attachments', params });
    return handleServiceError(e);
  }
});
