import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  AggregateDisputesCommand,
  GetDisputeAttachmentsByDisputeIdCommand,
  GetDisputeByIdCommand,
  GetDisputeFacetsCommand,
  GetDisputeNotesByDisputeIdCommand,
  ListDisputesCommand,
  ListDisputesMetaCommand,
  StreamDisputesCommand,
} from '@dbd/reporting-disputes/dispute.commands';
import { AggregateDisputesResponse, ListDisputesResponse } from '@dbd/reporting-disputes/dispute.dto.js';
import { DisputeAttachmentRow, DisputeNoteRow, DisputeRow } from '@dbd/reporting-disputes/dispute.types.js';
import { handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { FacetedSearchResponse, PaginatedResult } from '@dbd/zod-types';
import { ListMeta } from '@dbd/zod-types-common';

import { getDisputeService } from './dispute.service.js';

const logger = getLogger('dispute.loaders');

export async function getDisputeById(params: Omit<GetDisputeByIdCommand, 'authUser'>): Promise<DisputeRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getDisputeService();
    const data = await service.getDisputeById(
      GetDisputeByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting dispute by id', params });
    throw handleNextError(error);
  }
}

export async function listDisputes(params: Omit<ListDisputesCommand, 'authUser'>): Promise<ListDisputesResponse> {
  try {
    const user = await requireUser();

    const service = await getDisputeService();
    const data = await service.listDisputes(
      ListDisputesCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing disputes', params });
    throw handleNextError(error);
  }
}

export async function listDisputesMeta(params: Omit<ListDisputesMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getDisputeService();
    const data = await service.listDisputesMeta(
      ListDisputesMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing disputes meta', params });
    throw handleNextError(error);
  }
}

export async function aggregateDisputes(
  params: Omit<AggregateDisputesCommand, 'authUser'>,
): Promise<AggregateDisputesResponse> {
  try {
    const user = await requireUser();

    const service = await getDisputeService();
    const data = await service.aggregateDisputes(
      AggregateDisputesCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error aggregating disputes', params });
    throw handleNextError(error);
  }
}

export async function streamDisputes(
  params: Omit<StreamDisputesCommand, 'authUser'>,
): Promise<AsyncGenerator<DisputeRow[]>> {
  try {
    const user = await requireUser();

    const service = await getDisputeService();
    const data = await service.streamDisputes(
      StreamDisputesCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming disputes', params });
    throw handleNextError(error);
  }
}

export async function getDisputeFacets(
  params: Omit<GetDisputeFacetsCommand, 'authUser'>,
): Promise<FacetedSearchResponse> {
  try {
    const user = await requireUser();

    const service = await getDisputeService();
    const data = await service.getDisputeFacets(
      GetDisputeFacetsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting dispute facets', params });
    throw handleNextError(error);
  }
}

export async function getDisputeNotesByDisputeId(
  params: Omit<GetDisputeNotesByDisputeIdCommand, 'authUser'>,
): Promise<PaginatedResult<typeof DisputeNoteRow>> {
  try {
    const user = await requireUser();

    const service = await getDisputeService();
    const data = await service.getDisputeNotesByDisputeId(
      GetDisputeNotesByDisputeIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting dispute notes by dispute id', params });
    throw handleNextError(error);
  }
}

export async function getDisputeAttachmentsByDisputeId(
  params: Omit<GetDisputeAttachmentsByDisputeIdCommand, 'authUser'>,
): Promise<PaginatedResult<typeof DisputeAttachmentRow>> {
  try {
    const user = await requireUser();

    const service = await getDisputeService();
    const data = await service.getDisputeAttachmentsByDisputeId(
      GetDisputeAttachmentsByDisputeIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting dispute attachments by dispute id', params });
    throw handleNextError(error);
  }
}
