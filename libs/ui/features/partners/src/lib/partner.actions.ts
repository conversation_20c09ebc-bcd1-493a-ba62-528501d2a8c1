'use server';
import 'server-only';

import { ReadWriteRoles } from '@dbd/core-types/constants/auth.constants';
import { UserEntityTypeEnum } from '@dbd/core-types/enums/common';
import type { CreatePartnerUserSchema, EditPartnerUserSchema } from '@dbd/reporting-partners/partner.types';
import { getAuthenticatedClient, handleServiceError, withSentryAction } from '@dbd/ui/lib/server';
import { hasGroups } from '@dbd/ui/lib/user';
import { getLogger } from '@dbd/utils/logging';
import {
  AuthUserSelfSubType,
  ForbiddenError,
  NotFoundError,
  type PartnerId,
  type UserId,
  type UserStatus,
} from '@dbd/zod-types-common';
import type { PlaidAccount } from 'react-plaid-link';

import { getPartnerById, getPartnerUserById } from './partner.loaders.js';

const logger = getLogger('partner.actions');

export const updatePartnerUserStatus = withSentryAction(async function updatePartnerUserStatus(params: {
  userId: UserId;
  status: UserStatus;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const partnerUser = await getPartnerUserById({
      params: {
        id: params.userId,
        fields: [],
      },
    });
    if (!partnerUser) {
      throw new NotFoundError({ cause: new Error(`Partner user not found: ${params.userId}`) });
    }

    if (user.tenant_id !== partnerUser.tenant_id || (user.partner_id && user.partner_id !== partnerUser.partner_id)) {
      throw new ForbiddenError({ cause: new Error(`User is not allowed to update partner user ${params.userId}`) });
    }

    const isAdmin = hasGroups(user, ['TENANT_ADMIN', 'PARTNER_ADMIN']);
    if (!isAdmin) {
      throw new ForbiddenError({ cause: new Error(`User is not allowed to update partner user ${params.userId}`) });
    }

    const response = await client.users.updateStatus(partnerUser.id, params.status);
    return { success: true as const, data: response };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error updating partner user status', params });
    return handleServiceError(e);
  }
});

export const editPartnerUser = withSentryAction(async function editPartnerUser(params: EditPartnerUserSchema) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const partnerUser = await getPartnerUserById({
      params: {
        id: params.user_id,
        fields: [],
      },
    });
    if (!partnerUser) {
      throw new NotFoundError({ cause: new Error(`Partner user not found: ${params.user_id}`) });
    }

    if (user.tenant_id !== partnerUser.tenant_id || (user.partner_id && user.partner_id !== partnerUser.partner_id)) {
      throw new ForbiddenError({ cause: new Error(`User is not allowed to edit partner user ${params.user_id}`) });
    }

    const isAdmin = hasGroups(user, ['TENANT_ADMIN', 'PARTNER_ADMIN']);
    if (!isAdmin) {
      throw new ForbiddenError({ cause: new Error(`User is not allowed to edit partner user ${params.user_id}`) });
    }

    const response = await client.users.update(partnerUser.id, {
      type: 'PARTNER',
      first_name: params.first_name,
      last_name: params.last_name,
      email: params.email,
      group: params.group,
      phone_number: params.phone_number,
    });

    return { success: true as const, data: response };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error editing partner user', params });
    return handleServiceError(e);
  }
});

export const createPartnerUser = withSentryAction(async function createPartnerUser(params: CreatePartnerUserSchema) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const isAdmin = hasGroups(user, ['TENANT_ADMIN', 'PARTNER_ADMIN']);
    if (!isAdmin) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to create partner user for partner ${params.partner_id}`),
      });
    }

    if (user.partner_id && user.partner_id !== params.partner_id) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to create partner user for partner ${params.partner_id}`),
      });
    }

    const partner = await getPartnerById({
      params: {
        id: params.partner_id,
        fields: [],
      },
    });
    if (!partner) {
      throw new NotFoundError({ cause: new Error(`Partner not found: ${params.partner_id}`) });
    }

    if (partner.tenant_id !== user.tenant_id) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to create partner user for partner ${params.partner_id}`),
      });
    }

    const response = await client.users.create(
      {
        first_name: params.first_name,
        last_name: params.last_name,
        email: params.email,
        group: params.group,
        type: 'PARTNER',
        phone_number: params.phone_number,
      },
      {
        partnerId: params.partner_id,
      },
    );

    return { success: true as const, data: response };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error creating partner user', params });
    return handleServiceError(e);
  }
});

export const generatePartnerLinkToken = withSentryAction(async function generatePartnerLinkToken(params: {
  partnerId: PartnerId;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const canAddBankAccount = user.type === 'PARTNER' && user.roles.includes(ReadWriteRoles.PARTNER_BANK_ACCOUNT_WRITE);
    if (!canAddBankAccount || params.partnerId !== user.partner_id) {
      throw new ForbiddenError();
    }

    const partner = await getPartnerById({
      params: {
        id: params.partnerId,
        fields: [],
      },
    });
    if (!partner) {
      throw new NotFoundError({ cause: new Error(`Partner not found: ${params.partnerId}`) });
    }

    const linkToken = await client.bankIdentity.linkToken(
      partner.partner_id,
      UserEntityTypeEnum.PARTNER,
      undefined,
      false,
    );

    return {
      success: true as const,
      data: linkToken,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error generating partner link token', params });
    return handleServiceError(e);
  }
});

export type ExchangePartnerPublicTokenParams = {
  partnerId: PartnerId;
  publicToken: string;
  isManual?: boolean;
  account?: PlaidAccount;
};
export const exchangePartnerPublicToken = withSentryAction(async function exchangePartnerPublicToken(
  params: ExchangePartnerPublicTokenParams,
) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const canAddBankAccount = user.type === 'PARTNER' && user.roles.includes(ReadWriteRoles.PARTNER_BANK_ACCOUNT_WRITE);
    if (!canAddBankAccount || params.partnerId !== user.partner_id) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to exchange public token for partner ${params.partnerId}`),
      });
    }

    const partner = await getPartnerById({
      params: {
        id: params.partnerId,
        fields: [],
      },
    });
    if (!partner) {
      throw new NotFoundError({ cause: new Error(`Partner not found: ${params.partnerId}`) });
    }

    await client.bankIdentity.exchangePublicToken(
      params.publicToken,
      AuthUserSelfSubType.enum.PARTNER,
      partner.partner_id,
      params.isManual,
      params.account,
    );

    return {
      success: true as const,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error exchanging partner public token', params });
    return handleServiceError(e);
  }
});
