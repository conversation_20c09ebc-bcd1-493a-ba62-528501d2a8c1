import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  GetPartnerByDomainCommand,
  GetPartnerByIdCommand,
  GetPartnerProcessingPlanByIdCommand,
  GetPartnerUserByIdCommand,
  ListPartnerBankAccountsCommand,
  ListPartnerMccCodesCommand,
  ListPartnerProcessingPlansCommand,
  ListPartnersCommand,
  ListPartnersMetaCommand,
  ListPartnerUsersCommand,
  StreamPartnersCommand,
} from '@dbd/reporting-partners/partner.commands';
import {
  ListPartnerBankAccountsResponse,
  ListPartnerMccCodesResponse,
  ListPartnerProcessingPlansResponse,
  ListPartnersResponse,
  ListPartnerUsersResponse,
} from '@dbd/reporting-partners/partner.dto';
import { PartnerProcessingPlanRow, PartnerRow, PartnerUserRow } from '@dbd/reporting-partners/partner.types.js';
import { getAuthenticatedClient, handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { OrderDirection } from '@dbd/zod-types';
import type { ListMeta, PartnerId } from '@dbd/zod-types-common';

import { getPartnerService } from './partner.service.js';

const logger = getLogger('partner.loaders');

export async function getPartnerById(params: Omit<GetPartnerByIdCommand, 'authUser'>): Promise<PartnerRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.getPartnerById(
      GetPartnerByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting partner by domain', params });
    throw handleNextError(error);
  }
}

export async function getPartnerByDomain(
  params: Omit<GetPartnerByDomainCommand, 'authUser'>,
): Promise<PartnerRow | undefined> {
  try {
    const service = await getPartnerService();
    const data = await service.getPartnerByDomain(params);
    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing partners', params });
    throw handleNextError(error);
  }
}

export async function listPartners(params: Omit<ListPartnersCommand, 'authUser'>): Promise<ListPartnersResponse> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.listPartners(
      ListPartnersCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing partners', params });
    throw handleNextError(error);
  }
}

export async function listPartnerNames(
  params: Omit<ListPartnersCommand, 'authUser'>,
): Promise<{ data: { partner_name: string; partner_id: PartnerId }[] }> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.listPartners(
      ListPartnersCommand.parse({
        ...params,
        params: {
          ...params.params,
          fields: ['partner_name', 'partner_id'],
          orderBy: [['partner_name', OrderDirection.ASC]],
        },
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing partners meta', params });
    throw handleNextError(error);
  }
}

export async function listPartnersMeta(params: Omit<ListPartnersMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.listPartnersMeta(
      ListPartnersMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing partners meta', params });
    throw handleNextError(error);
  }
}

export async function streamPartners(
  params: Omit<StreamPartnersCommand, 'authUser'>,
): Promise<AsyncGenerator<PartnerRow[]>> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.streamPartners(
      StreamPartnersCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming partners', params });
    throw handleNextError(error);
  }
}

export async function getPartnerProcessingPlanById(
  params: Omit<GetPartnerProcessingPlanByIdCommand, 'authUser'>,
): Promise<PartnerProcessingPlanRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.getPartnerProcessingPlanById(
      GetPartnerProcessingPlanByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting partner processing plan by id', params });
    throw handleNextError(error);
  }
}

export async function listPartnerProcessingPlans(
  params: Omit<ListPartnerProcessingPlansCommand, 'authUser'>,
): Promise<ListPartnerProcessingPlansResponse> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.listPartnerProcessingPlans(
      ListPartnerProcessingPlansCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing partner processing plans', params });
    throw handleNextError(error);
  }
}

export async function listPartnerMccCodes(
  params: Omit<ListPartnerMccCodesCommand, 'authUser'>,
): Promise<ListPartnerMccCodesResponse> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.listPartnerMccCodes(
      ListPartnerMccCodesCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing partner mcc codes', params });
    throw handleNextError(error);
  }
}

export async function listPartnerBankAccounts(
  params: Omit<ListPartnerBankAccountsCommand, 'authUser'>,
): Promise<ListPartnerBankAccountsResponse> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.listPartnerBankAccounts(
      ListPartnerBankAccountsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing partner bank accounts', params });
    throw handleNextError(error);
  }
}

export async function getPartnerUserById(
  params: Omit<GetPartnerUserByIdCommand, 'authUser'>,
): Promise<PartnerUserRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.getPartnerUserById(
      GetPartnerUserByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting partner user by id', params });
    throw handleNextError(error);
  }
}

export async function listPartnerUsers(
  params: Omit<ListPartnerUsersCommand, 'authUser'>,
): Promise<ListPartnerUsersResponse> {
  try {
    const user = await requireUser();

    const service = await getPartnerService();
    const data = await service.listPartnerUsers(
      ListPartnerUsersCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing partner users', params });
    throw handleNextError(error);
  }
}

export async function getSvixAppUrl(
  partnerId: PartnerId,
): Promise<{ success: true; svixUrl: string } | { success: false; error: string }> {
  const { client } = await getAuthenticatedClient();
  try {
    const response = await client.svix.getAppUrl(partnerId);
    return {
      success: true as const,
      svixUrl: response.url,
    };
  } catch (error: unknown) {
    logger.error({ err: error, message: 'Error getting svix app url', params: { partnerId } });
    return {
      success: false as const,
      error: (error as Error).message || 'Failed to get svix app url',
    };
  }
}
