import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  AggregateTransactionsCommand,
  GetTransactionByIdCommand,
  GetTransactionFacetsCommand,
  ListTransactionsCommand,
  ListTransactionsMetaCommand,
  StreamTransactionsCommand,
} from '@dbd/reporting-transactions/transaction.commands';
import {
  AggregateTransactionsResponse,
  ListTransactionsResponse,
} from '@dbd/reporting-transactions/transaction.dto.js';
import { TransactionRow } from '@dbd/reporting-transactions/transaction.types.js';
import { handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { FacetedSearchResponse } from '@dbd/zod-types';
import { ListMeta } from '@dbd/zod-types-common';

import { getTransactionService } from './transaction.service.js';

const logger = getLogger('transaction.loaders');

export async function getTransactionById(
  params: Omit<GetTransactionByIdCommand, 'authUser'>,
): Promise<TransactionRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getTransactionService();
    const data = await service.getTransactionById(
      GetTransactionByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );
    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting transaction by id', params });
    throw handleNextError(error);
  }
}

export async function listTransactions(
  params: Omit<ListTransactionsCommand, 'authUser'>,
): Promise<ListTransactionsResponse> {
  try {
    const user = await requireUser();

    const service = await getTransactionService();
    const data = await service.listTransactions(
      ListTransactionsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing transactions', params });
    throw handleNextError(error);
  }
}

export async function listTransactionsMeta(params: Omit<ListTransactionsMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getTransactionService();
    const data = await service.listTransactionsMeta(
      ListTransactionsMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing transactions meta', params });
    throw handleNextError(error);
  }
}

export async function aggregateTransactions(
  params: Omit<AggregateTransactionsCommand, 'authUser'>,
): Promise<AggregateTransactionsResponse> {
  try {
    const user = await requireUser();

    const service = await getTransactionService();
    const data = await service.aggregateTransactions(
      AggregateTransactionsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error aggregating transactions', params });
    throw handleNextError(error);
  }
}

export async function streamTransactions(
  params: Omit<StreamTransactionsCommand, 'authUser'>,
): Promise<AsyncGenerator<TransactionRow[]>> {
  try {
    const user = await requireUser();

    const service = await getTransactionService();
    const data = await service.streamTransactions(
      StreamTransactionsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming transactions', params });
    throw handleNextError(error);
  }
}

export async function getTransactionFacets(
  params: Omit<GetTransactionFacetsCommand, 'authUser'>,
): Promise<FacetedSearchResponse> {
  try {
    const user = await requireUser();

    const service = await getTransactionService();
    const data = await service.getTransactionFacets(
      GetTransactionFacetsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting transaction facets', params });
    throw handleNextError(error);
  }
}
