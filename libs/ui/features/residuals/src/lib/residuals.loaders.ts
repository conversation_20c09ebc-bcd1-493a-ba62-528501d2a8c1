import 'server-only';

import { getRetoolConfig, getRetoolEmbedUrl, handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';

const logger = getLogger('residuals.loaders');

export async function getResidualsDashboardUrl(): Promise<{ embedUrl: string; error?: boolean; message?: string }> {
  try {
    const config = getRetoolConfig();
    return await getRetoolEmbedUrl(config.apps.residualReports);
  } catch (error) {
    logger.error({ err: error, message: 'Error getting residuals dashboard url' });
    throw handleNextError(error);
  }
}
