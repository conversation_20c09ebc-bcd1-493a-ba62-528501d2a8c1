{"compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "files": [], "include": [], "references": [{"path": "../../../utils"}, {"path": "../../../reporting/auth"}, {"path": "../../../zod-types"}, {"path": "../../../zod-types-common"}, {"path": "../common"}, {"path": "../data-table"}, {"path": "../../../tailwind-components"}, {"path": "../../../reporting/contractors"}, {"path": "../../../reporting/common/server"}, {"path": "../../../next-sessions"}, {"path": "../../../core-types"}, {"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../../tsconfig.base.json"}