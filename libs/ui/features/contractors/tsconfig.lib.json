{"extends": "../../../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "baseUrl": ".", "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts", "next", "@nx/next/typings/image.d.ts"]}, "exclude": ["**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.spec.js", "**/*.test.js", "**/*.spec.jsx", "**/*.test.jsx", "vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"], "references": [{"path": "../../../utils/tsconfig.lib.json"}, {"path": "../../../reporting/auth/tsconfig.lib.json"}, {"path": "../../../zod-types/tsconfig.lib.json"}, {"path": "../../../zod-types-common/tsconfig.lib.json"}, {"path": "../common/tsconfig.lib.json"}, {"path": "../data-table/tsconfig.lib.json"}, {"path": "../../../tailwind-components/tsconfig.lib.json"}, {"path": "../../../reporting/contractors/tsconfig.lib.json"}, {"path": "../../../reporting/common/server/tsconfig.lib.json"}, {"path": "../../../next-sessions/tsconfig.lib.json"}, {"path": "../../../core-types/tsconfig.lib.json"}]}