'use server';
import 'server-only';

import { ReadWriteRoles } from '@dbd/core-types/constants/auth.constants';
import { ContractorParentType } from '@dbd/core-types/enums/contractor.enums';
import { ContractorMap } from '@dbd/core-types/map/contractor.map';
import { getAuthenticatedClient, handleServiceError, withSentryAction } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils/logging';
import { ContractorId } from '@dbd/zod-types-common';
import { ForbiddenError } from '@dbd/zod-types-common/errors';

const logger = getLogger('contractor.actions');

export const createContractorApplicationLink = withSentryAction(async function createContractorApplicationLink() {
  try {
    const { user, client } = await getAuthenticatedClient();

    const parentId = user.business_id ?? user.partner_id;
    const parentType = user.business_id ? ContractorParentType.BUSINESS : ContractorParentType.PARTNER;

    if (!parentId) {
      throw new ForbiddenError({
        title: 'Insufficient permissions',
        detail: 'Your account is not allowed to generate contractor application links.',
      });
    }
    if (parentType === ContractorParentType.PARTNER && !user.roles.includes(ReadWriteRoles.PARTNER_CONTRACTORS_LINK)) {
      throw new ForbiddenError({
        title: 'Insufficient permissions',
        detail: 'Your organization roles do not allow generating partner contractor application links.',
      });
    }
    if (parentType === ContractorParentType.BUSINESS) {
      const contractorsEnabled = user.partner_settings?.contractors_enabled ?? false;
      if (!contractorsEnabled) {
        throw new ForbiddenError({
          title: 'Contractors disabled',
          detail: 'Generating contractor application links is not allowed for this user.',
        });
      }
      if (!user.roles.includes(ReadWriteRoles.BUSINESS_CONTRACTORS_LINK)) {
        throw new ForbiddenError({
          title: 'Insufficient permissions',
          detail: 'Your organization roles do not allow generating contractor application links.',
        });
      }
    }

    const linkDTO = await client.contractors.generateLink(parentId, parentType);
    const link = ContractorMap.toLinkDomain(linkDTO);

    return {
      success: true as const,
      data: link,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error creating contractor application link' });
    return handleServiceError(e);
  }
});

export const activateContractor = withSentryAction(async function activateContractor(contractorId: ContractorId) {
  try {
    ContractorId.parse(contractorId); // Validate ID
    const { user, client } = await getAuthenticatedClient();
    const isAllowedToChangeStatus =
      user.partner_settings?.contractors_enabled ||
      user.roles.includes(ReadWriteRoles.PARTNER_CONTRACTORS_STATUS) ||
      user.roles.includes(ReadWriteRoles.BUSINESS_CONTRACTORS_STATUS);
    if (!isAllowedToChangeStatus) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to activate contractor ${contractorId}`),
      });
    }
    const responseDTO = await client.contractors.activate(contractorId);
    const contractor = ContractorMap.toDomain(responseDTO);
    return {
      success: true as const,
      data: contractor,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error activating contractor', contractorId });
    return handleServiceError(e);
  }
});

export const deactivateContractor = withSentryAction(async function deactivateContractor(contractorId: ContractorId) {
  try {
    ContractorId.parse(contractorId); // Validate ID
    const { user, client } = await getAuthenticatedClient();
    const isAllowedToChangeStatus =
      user.partner_settings?.contractors_enabled ||
      user.roles.includes(ReadWriteRoles.PARTNER_CONTRACTORS_STATUS) ||
      user.roles.includes(ReadWriteRoles.BUSINESS_CONTRACTORS_STATUS);
    if (!isAllowedToChangeStatus) {
      throw new ForbiddenError({
        cause: new Error(`User is not allowed to deactivate contractor ${contractorId}`),
      });
    }
    const responseDTO = await client.contractors.deactivate(contractorId);
    const contractor = ContractorMap.toDomain(responseDTO);
    return {
      success: true as const,
      data: contractor,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error deactivating contractor', contractorId });
    return handleServiceError(e);
  }
});
