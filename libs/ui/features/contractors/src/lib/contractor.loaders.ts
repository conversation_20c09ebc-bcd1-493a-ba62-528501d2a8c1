import 'server-only';

import { USER_ROLES } from '@dbd/core-types/constants/auth.constants';
import { requireUser } from '@dbd/next-sessions/auth';
import { AuthUser } from '@dbd/reporting-auth/auth.types.js';
import {
  GetContractorByIdCommand,
  GetContractorFacetsCommand,
  ListContractorsCommand,
  ListContractorsMetaCommand,
  StreamContractorsCommand,
} from '@dbd/reporting-contractors/contractor.commands';
import { ListContractorsResponse } from '@dbd/reporting-contractors/contractor.dto';
import { ContractorRow } from '@dbd/reporting-contractors/contractor.types.js';
import { handleNextError } from '@dbd/ui/lib/server';
import { hasRole } from '@dbd/ui/lib/user';
import { getLogger } from '@dbd/utils';
import { FacetedSearchResponse } from '@dbd/zod-types';
import { ForbiddenError, ListMeta } from '@dbd/zod-types-common';

import { getContractorService } from './contractor.service.js';

const logger = getLogger('contractor.loaders');

const validatePermissions = (user: AuthUser) => {
  if (user.business_id) {
    return hasRole(user, USER_ROLES.business.contractors.read);
  }

  return hasRole(user, USER_ROLES.partner.contractors.read);
};

export async function getContractorById(
  params: Omit<GetContractorByIdCommand, 'authUser'>,
): Promise<ContractorRow | undefined> {
  try {
    const user = await requireUser();
    if (!validatePermissions(user)) {
      throw ForbiddenError.fromError(new Error('User does not have permission to access this resource'));
    }

    const service = await getContractorService();
    const data = await service.getContractorById(
      GetContractorByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing contractors', params });
    throw handleNextError(error);
  }
}

export async function listContractors(
  params: Omit<ListContractorsCommand, 'authUser'>,
): Promise<ListContractorsResponse> {
  try {
    const user = await requireUser();
    if (!validatePermissions(user)) {
      throw ForbiddenError.fromError(new Error('User does not have permission to access this resource'));
    }

    const service = await getContractorService();
    const data = await service.listContractors(
      ListContractorsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return ListContractorsResponse.parse(data);
  } catch (error) {
    logger.error({ err: error, message: 'Error listing contractor names', params });
    throw handleNextError(error);
  }
}

export async function listContractorNames(
  params: Omit<ListContractorsCommand, 'authUser'>,
): Promise<ListContractorsResponse> {
  try {
    const user = await requireUser();
    if (!validatePermissions(user)) {
      throw ForbiddenError.fromError(new Error('User does not have permission to access this resource'));
    }

    const service = await getContractorService();
    const data = await service.listContractors(
      ListContractorsCommand.parse({
        ...params,
        params: {
          ...params.params,
          fields: ['parent_name', 'parent_id'],
        },
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing contractors meta', params });
    throw handleNextError(error);
  }
}

export async function listContractorsMeta(params: Omit<ListContractorsMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();
    if (!validatePermissions(user)) {
      throw ForbiddenError.fromError(new Error('User does not have permission to access this resource'));
    }

    const service = await getContractorService();
    const data = await service.listContractorsMeta(
      ListContractorsMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming contractors', params });
    throw handleNextError(error);
  }
}

export async function streamContractors(
  params: Omit<StreamContractorsCommand, 'authUser'>,
): Promise<AsyncGenerator<ContractorRow[]>> {
  try {
    const user = await requireUser();
    if (!validatePermissions(user)) {
      throw ForbiddenError.fromError(new Error('User does not have permission to access this resource'));
    }

    const service = await getContractorService();
    const data = await service.streamContractors(
      StreamContractorsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting contractor facets', params });
    throw handleNextError(error);
  }
}

export async function getContractorFacets(
  params: Omit<GetContractorFacetsCommand, 'authUser'>,
): Promise<FacetedSearchResponse> {
  try {
    const user = await requireUser();
    if (!validatePermissions(user)) {
      throw ForbiddenError.fromError(new Error('User does not have permission to access this resource'));
    }

    const service = await getContractorService();
    const data = await service.getContractorFacets(
      GetContractorFacetsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting contractor facets', params });
    throw handleNextError(error);
  }
}
