import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  AggregateRefundsCommand,
  GetRefundByIdCommand,
  ListRefundsCommand,
  ListRefundsMetaCommand,
  StreamRefundsCommand,
} from '@dbd/reporting-refunds/refund.commands';
import { AggregateRefundsResponse, ListRefundsResponse } from '@dbd/reporting-refunds/refund.dto.js';
import { RefundRow } from '@dbd/reporting-refunds/refund.types.js';
import { handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { ListMeta } from '@dbd/zod-types-common';

import { getRefundService } from './refund.service.js';

const logger = getLogger('refund.loaders');

export async function getRefundById(params: Omit<GetRefundByIdCommand, 'authUser'>): Promise<RefundRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getRefundService();
    const data = await service.getRefundById(
      GetRefundByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting refund by id', params });
    throw handleNextError(error);
  }
}

export async function listRefunds(params: Omit<ListRefundsCommand, 'authUser'>): Promise<ListRefundsResponse> {
  try {
    const user = await requireUser();

    const service = await getRefundService();
    const data = await service.listRefunds(
      ListRefundsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing refunds', params });
    throw handleNextError(error);
  }
}

export async function listRefundsMeta(params: Omit<ListRefundsMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getRefundService();
    const data = await service.listRefundsMeta(
      ListRefundsMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing refunds meta', params });
    throw handleNextError(error);
  }
}

export async function aggregateRefunds(
  params: Omit<AggregateRefundsCommand, 'authUser'>,
): Promise<AggregateRefundsResponse> {
  try {
    const user = await requireUser();

    const service = await getRefundService();
    const data = await service.aggregateRefunds(
      AggregateRefundsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error aggregating refunds', params });
    throw handleNextError(error);
  }
}

export async function streamRefunds(
  params: Omit<StreamRefundsCommand, 'authUser'>,
): Promise<AsyncGenerator<RefundRow[]>> {
  try {
    const user = await requireUser();

    const service = await getRefundService();
    const data = await service.streamRefunds(
      StreamRefundsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming refunds', params });
    throw handleNextError(error);
  }
}
