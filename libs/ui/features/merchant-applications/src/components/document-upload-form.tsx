'use client';

import { FileDocumentTypes, FileUnderwritingDocumentTypes } from '@dbd/core-types/constants/file.constants';
import { Button, type ButtonProps } from '@dbd/ui/components/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from '@dbd/ui/components/dialog';
import { Form, FormField, FormMessage } from '@dbd/ui/components/form';
import { cn } from '@dbd/ui/lib/utils';
import { SelectInput } from '@dbd/ui-forms/components/select-form-input';
import type { BusinessId, MerchantApplicationId } from '@dbd/zod-types-common/constants';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { createContext, useCallback, useContext, useId, useMemo, useState, useTransition } from 'react';
import { useFieldArray, useForm, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { uploadMerchantApplicationDocuments } from '../lib/merchant-application.actions.js';

const FileUploadSchema = z.object({
  files: z.array(z.object({ type: z.nativeEnum(FileUnderwritingDocumentTypes), file: z.instanceof(File) })),
});
export type FileUploadSchema = z.infer<typeof FileUploadSchema>;

const TypeOptions = [
  { label: 'Alien Registration or Visa', value: FileDocumentTypes.ALIEN_REGISTRATION_OR_VISA },
  { label: 'Articles of Incorporation', value: FileDocumentTypes.ARTICLES_OF_INCORPORATION },
  { label: 'Business Bank Statement', value: FileDocumentTypes.BUSINESS_BANK_STATEMENT },
  { label: 'Business Phone Bill', value: FileDocumentTypes.BUSINESS_PHONE_BILL },
  { label: 'Business Tax Return', value: FileDocumentTypes.BUSINESS_TAX_RETURN },
  { label: 'Drivers License', value: FileDocumentTypes.DRIVERS_LICENSE },
  { label: 'Government Business License', value: FileDocumentTypes.GOVERNMENT_BUSINESS_LICENSE },
  { label: 'Government Photo ID', value: FileDocumentTypes.GOVERNMENT_PHOTO_ID },
  { label: 'IRS EIN Letter (Response to Form SS-4)', value: FileDocumentTypes.IRS_EIN_LETTER },
  { label: 'IRS Tax-Exempt Determination Letter', value: FileDocumentTypes.IRS_TAX_EXEMPT_LETTER },
  { label: 'Operating Agreement', value: FileDocumentTypes.OPERATING_AGREEMENT },
  { label: 'Other', value: FileDocumentTypes.OTHER },
  { label: 'Other Registration Document', value: FileDocumentTypes.OTHER_REGISTRATION_DOCUMENT },
  { label: 'Passport', value: FileDocumentTypes.PASSPORT },
  { label: 'Personal Bank Statement', value: FileDocumentTypes.PERSONAL_BANK_STATEMENT },
  { label: 'Personal Phone Bill', value: FileDocumentTypes.PERSONAL_PHONE_BILL },
  { label: 'Rental or Lease Agreement', value: FileDocumentTypes.RENTAL_OR_LEASE_AGREEMENT },
  { label: 'Social Security Card', value: FileDocumentTypes.SOCIAL_SECURITY_CARD },
  { label: 'Utility Bill', value: FileDocumentTypes.UTILITY_BILL },
];

const FileInput = React.forwardRef<
  HTMLButtonElement,
  ButtonProps & { onFileUploaded: (files: FileList | null) => void }
>(({ onFileUploaded, children, ...props }, ref) => {
  const id = useId();

  return (
    <>
      <input id={id} type="file" multiple onChange={(e) => onFileUploaded(e.target.files)} className="hidden" />
      <Button ref={ref} {...props}>
        <label htmlFor={id} className="flex size-full cursor-pointer items-center justify-center">
          {children}
        </label>
      </Button>
    </>
  );
});
FileInput.displayName = 'FileInput';

export type DocumentUploadFormContext = {
  transition: boolean;
  form: UseFormReturn<FileUploadSchema>;
  handleSubmit: (e: React.MouseEvent<HTMLButtonElement>) => void;
};

export const DocumentUploadFormContext = createContext<DocumentUploadFormContext | undefined>(undefined);

export const useDocumentUploadForm = () => {
  const context = useContext(DocumentUploadFormContext);
  if (!context) {
    throw new Error('DocumentUploadFormContext not found');
  }
  return context;
};

export type DocumentUploadFormProps = {
  businessId?: BusinessId;
  applicationId: MerchantApplicationId;
  children: (context: DocumentUploadFormContext) => React.ReactNode;
  onSuccess?: () => void;
};
export const DocumentUploadForm = ({ businessId, applicationId, children, onSuccess }: DocumentUploadFormProps) => {
  const [transition, startTransition] = useTransition();
  const form = useForm<FileUploadSchema>({
    resolver: zodResolver(FileUploadSchema),
    defaultValues: {
      files: [],
    },
  });

  const files = useFieldArray({
    control: form.control,
    name: 'files',
  });

  const onSubmit = form.handleSubmit((data) => {
    startTransition(async () => {
      const formData = new FormData();
      formData.append('applicationId', applicationId.toString());
      formData.append('businessId', businessId?.toString() ?? '');
      for (const { type, file } of data.files) {
        formData.append('types', type);
        formData.append('files', file, file.name);
      }

      const response = await uploadMerchantApplicationDocuments(formData);
      if (!response.success && 'data' in response) {
        for (const error of response.data) {
          if (error.error) {
            form.setError(`files.${error.index}`, { message: error.error });
          } else {
            files.remove(error.index);
          }
        }

        return;
      }

      if (onSuccess) {
        onSuccess();
      }
    });
  });

  const handleSubmit = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();
      onSubmit(e);
    },
    [onSubmit],
  );

  const context = useMemo(
    () => ({
      transition,
      form,
      handleSubmit,
    }),
    [transition, form, handleSubmit],
  );

  return (
    <DocumentUploadFormContext.Provider value={context}>
      <Form {...form}>{children(context)}</Form>
    </DocumentUploadFormContext.Provider>
  );
};

export const DocumentUploadFormContent = () => {
  const { form } = useDocumentUploadForm();
  const errors = form.formState.errors;

  const files = useFieldArray({
    control: form.control,
    name: 'files',
  });

  const onFileChange = (list: FileList | null) => {
    for (const file of list || []) {
      files.append({
        file,
        type: FileDocumentTypes.OTHER,
      });
    }
  };

  return (
    <>
      {files.fields.length === 0 && (
        <div className="border-foreground-200 flex w-full items-center justify-center rounded-md border border-dashed p-2">
          <FileInput size="xs" variant="link" onFileUploaded={onFileChange}>
            Add Files
          </FileInput>
        </div>
      )}
      {files.fields.length > 0 && (
        <div className="flex flex-col gap-4">
          {files.fields.map((file, index) => (
            <FormField
              key={file.id}
              control={form.control}
              name={`files.${index}`}
              render={() => (
                <div className="flex flex-col gap-4">
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        'w-1/2 overflow-hidden text-ellipsis text-xs leading-none h-full',
                        errors.files && errors.files[index] ? 'text-red-500' : '',
                      )}
                    >
                      {file.file.name}
                    </div>
                    <FormField
                      control={form.control}
                      name={`files.${index}.type`}
                      render={({ field }) => (
                        <SelectInput field={field} options={TypeOptions} size="sm" className="w-1/2" />
                      )}
                    />
                  </div>

                  <FormMessage className="w-full" />
                </div>
              )}
            />
          ))}
        </div>
      )}
    </>
  );
};

export const DocumentUploadDialog = ({
  businessId,
  applicationId,
  trigger,
  asChild = false,
}: {
  businessId?: BusinessId;
  applicationId: MerchantApplicationId;
  trigger?: React.ReactNode;
  asChild?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const onSuccess = useCallback(() => {
    setIsOpen(false);
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild={asChild}>
        {trigger || (
          <Button size="sm" variant="primary" className="w-full text-sm">
            Upload Documents
          </Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Upload Documents</DialogTitle>
        </DialogHeader>
        {isOpen && (
          <DocumentUploadForm applicationId={applicationId} businessId={businessId} onSuccess={onSuccess}>
            {({ transition, form, handleSubmit }) => (
              <>
                <DocumentUploadFormContent />

                <DialogFooter className="flex justify-end items-end">
                  <Button variant="secondary" disabled={transition} onClick={() => setIsOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    disabled={transition || form.getValues('files').length === 0}
                    onClick={handleSubmit}
                  >
                    Submit
                  </Button>
                </DialogFooter>
              </>
            )}
          </DocumentUploadForm>
        )}
      </DialogContent>
    </Dialog>
  );
};
