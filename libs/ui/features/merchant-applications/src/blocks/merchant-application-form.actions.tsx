'use server';

import {
  MerchantApplicationDeliveryTimeSchema,
  MerchantApplicationIndustryVolumeSchema,
  MerchantApplicationPaymentMixSchema,
} from '@dbd/reporting-merchant-apps/merchant-application.types.js';
import { MerchantApplicationRow } from '@dbd/reporting-merchant-apps/merchant-application.types.js';
import { toCents } from '@dbd/ui/lib/formatters';
import { getChangedValues, SubmitHandler } from '@dbd/ui-forms/lib/utils';
import { ProcessingPlanFormSchema } from '@dbd/ui-partners/schemas/processing-plan-form-schema';
import { getLogger } from '@dbd/utils';
import { Address, MerchantApplicationId, PartnerProcessingPlanId } from '@dbd/zod-types-common';
import { BadRequestError, BadRequestResponse, ServerError, ServerErrorResponse } from '@dbd/zod-types-common/errors';
import * as Sentry from '@sentry/nextjs';

import { createMerchantApplicationDraft, updateMerchantApplicationDraft } from '../lib/merchant-application.actions.js';
import { CompanyFormSchema, MerchantApplicationFormSchema } from '../schemas/merchant-application-form-schema.js';
import { MerchantApplicationFormState } from './merchant-application-form.js';

const logger = getLogger('merchant-application-form-actions');

const mapPlanFormSchemaToCreateSchema = (form: ProcessingPlanFormSchema) => {
  return {
    ...form,
    ach_txn_fee: toCents(form.ach_txn_fee),
    ach_max_fee: toCents(form.ach_max_fee),
    ach_reject_fee: toCents(form.ach_reject_fee),
    ach_reversal_fee: toCents(form.ach_reversal_fee),
    bank_linking_fee: toCents(form.bank_linking_fee),
    batch_fee: toCents(form.batch_fee),
    same_day_ach_txn_fee: toCents(form.same_day_ach_txn_fee),
    same_day_ach_max_fee: toCents(form.same_day_ach_max_fee),
    auth_cnp_fee: toCents(form.auth_cnp_fee),
    auth_cp_fee: toCents(form.auth_cp_fee),
    application_fee: toCents(form.application_fee),
    chargeback_fee: toCents(form.chargeback_fee),
    dda_reject_fee: toCents(form.dda_reject_fee),
    early_cancellation_fee: toCents(form.early_cancellation_fee),
    gateway_fee: toCents(form.gateway_fee),
    monthly_minimum_fee: toCents(form.monthly_minimum_fee),
    pci_non_compliance_fee: toCents(form.pci_non_compliance_fee),
    platform_fee: toCents(form.platform_fee),
    token_fee: toCents(form.token_fee),
    regulatory_product_fee: toCents(form.regulatory_product_fee),
    retrieval_fee: toCents(form.retrieval_fee),
    comment: form.comment || '',
  };
};

const mapApplicationFormSchemaToCreateSchema = (params: MerchantApplicationFormSchema) => {
  const company = CompanyFormSchema.safeParse(params.company);
  const industryVolume = MerchantApplicationIndustryVolumeSchema.safeParse(params.industry_volume);
  const transactionModes = MerchantApplicationPaymentMixSchema.safeParse(params.transaction_modes);
  const serviceDeliveries = MerchantApplicationDeliveryTimeSchema.safeParse(params.service_deliveries);
  const address = Address.safeParse(params.address);

  return {
    ...params,
    address: address.success ? address.data : undefined,
    company: company.success
      ? {
          ...company.data,
          tax_id: company.data.tax_id?.value as string | undefined,
          phone_number: company.data.phone_number?.replaceAll(/[\s().+-]/g, ''),
        }
      : undefined,
    owners: (params.owners ?? []).map((owner) => ({
      ...owner,
      ssn: (owner?.ssn?.value ?? undefined) as string | undefined,
      birth_date: owner?.birth_date?.toISOString().split('T')[0] ?? undefined,
      phone_number: owner?.phone_number?.replaceAll(/[\s().+-]/g, '') ?? undefined,
    })),
    industry_volume: industryVolume.success ? industryVolume.data : undefined,
    transaction_modes: transactionModes.success
      ? {
          in_person: transactionModes.data.in_person,
          online: transactionModes.data.online,
        }
      : undefined,
    service_deliveries: serviceDeliveries.success
      ? {
          same_day_delivery: serviceDeliveries.data.same_day_delivery,
          one_week_delivery: serviceDeliveries.data.one_week_delivery,
          two_week_delivery: serviceDeliveries.data.two_week_delivery,
          one_month_delivery: serviceDeliveries.data.one_month_delivery,
          more_than_one_month_delivery: serviceDeliveries.data.more_than_one_month_delivery,
        }
      : undefined,
    plan_id: params.plan?.id,
    plan: params.plan ? mapPlanFormSchemaToCreateSchema(params.plan) : undefined,
    sales_code_id: params.sales_code_id,
    sales_agent_id: params.sales_agent_id,
  };
};

export const CreateMerchantAppSubmitHandler: SubmitHandler<
  MerchantApplicationFormSchema,
  MerchantApplicationRow
> = async (data: MerchantApplicationFormSchema, state?: MerchantApplicationFormState) => {
  try {
    const params = mapApplicationFormSchemaToCreateSchema(data);
    if (state && !state.dirtyFields.plan) {
      delete params.plan;
    }

    const planId = params.plan_id;
    if (!planId) {
      return {
        success: false as const,
        error: BadRequestResponse.parse(
          new BadRequestError({
            errors: [
              {
                code: '400-01',
                path: ['plan_id'],
                message: 'Plan is required',
              },
            ],
          }).toJSON(),
        ),
      };
    }

    const response = await createMerchantApplicationDraft({
      params: {
        ...params,
        plan_id: planId,
      },
    });

    if (response.success === true) {
      return { success: true as const, data: response.data };
    }

    if (response.error.status === 400 && response.error.body.errors) {
      return { success: false as const, error: response.error.body };
    }

    if (response.error) {
      Sentry.captureException(response.error);
      return { success: false as const, error: response.error.body };
    }

    throw new Error('An unknown error occurred');
  } catch (e) {
    Sentry.captureException(e as Error);
    const err = ServerErrorResponse.parse(new ServerError({ cause: e }).toJSON());
    return { success: false as const, error: err };
  }
};

const mapPlanFormSchemaToUpdateSchema = (form: ProcessingPlanFormSchema) => {
  return {
    ...form,
    ach_txn_fee: typeof form.ach_txn_fee !== 'undefined' ? toCents(form.ach_txn_fee) : undefined,
    ach_max_fee: typeof form.ach_max_fee !== 'undefined' ? toCents(form.ach_max_fee) : undefined,
    ach_reject_fee: typeof form.ach_reject_fee !== 'undefined' ? toCents(form.ach_reject_fee) : undefined,
    ach_reversal_fee: typeof form.ach_reversal_fee !== 'undefined' ? toCents(form.ach_reversal_fee) : undefined,
    bank_linking_fee: typeof form.bank_linking_fee !== 'undefined' ? toCents(form.bank_linking_fee) : undefined,
    batch_fee: typeof form.batch_fee !== 'undefined' ? toCents(form.batch_fee) : undefined,
    same_day_ach_txn_fee:
      typeof form.same_day_ach_txn_fee !== 'undefined' ? toCents(form.same_day_ach_txn_fee) : undefined,
    same_day_ach_max_fee:
      typeof form.same_day_ach_max_fee !== 'undefined' ? toCents(form.same_day_ach_max_fee) : undefined,
    auth_cnp_fee: typeof form.auth_cnp_fee !== 'undefined' ? toCents(form.auth_cnp_fee) : undefined,
    auth_cp_fee: typeof form.auth_cp_fee !== 'undefined' ? toCents(form.auth_cp_fee) : undefined,
    application_fee: typeof form.application_fee !== 'undefined' ? toCents(form.application_fee) : undefined,
    chargeback_fee: typeof form.chargeback_fee !== 'undefined' ? toCents(form.chargeback_fee) : undefined,
    dda_reject_fee: typeof form.dda_reject_fee !== 'undefined' ? toCents(form.dda_reject_fee) : undefined,
    early_cancellation_fee:
      typeof form.early_cancellation_fee !== 'undefined' ? toCents(form.early_cancellation_fee) : undefined,
    gateway_fee: typeof form.gateway_fee !== 'undefined' ? toCents(form.gateway_fee) : undefined,
    monthly_minimum_fee:
      typeof form.monthly_minimum_fee !== 'undefined' ? toCents(form.monthly_minimum_fee) : undefined,
    pci_non_compliance_fee:
      typeof form.pci_non_compliance_fee !== 'undefined' ? toCents(form.pci_non_compliance_fee) : undefined,
    platform_fee: typeof form.platform_fee !== 'undefined' ? toCents(form.platform_fee) : undefined,
    token_fee: typeof form.token_fee !== 'undefined' ? toCents(form.token_fee) : undefined,
    regulatory_product_fee:
      typeof form.regulatory_product_fee !== 'undefined' ? toCents(form.regulatory_product_fee) : undefined,
    retrieval_fee: typeof form.retrieval_fee !== 'undefined' ? toCents(form.retrieval_fee) : undefined,
    comment: form.comment || undefined,
  };
};

const mapApplicationFormSchemaToUpdateSchema = (params: MerchantApplicationFormSchema) => {
  const company = CompanyFormSchema.safeParse(params.company);
  const industryVolume = MerchantApplicationIndustryVolumeSchema.safeParse(params.industry_volume);
  const transactionModes = MerchantApplicationPaymentMixSchema.safeParse(params.transaction_modes);
  const serviceDeliveries = MerchantApplicationDeliveryTimeSchema.safeParse(params.service_deliveries);
  const address = Address.safeParse(params.address);

  return {
    ...params,
    address: address.success ? address.data : undefined,
    company: company.success
      ? {
          ...company.data,
          tax_id: company.data.tax_id?.value as string | undefined,
          phone_number: company.data.phone_number?.replaceAll(/[\s().+-]/g, ''),
        }
      : undefined,
    owners: params.owners?.map((owner) => ({
      ...owner,
      ssn: (owner?.ssn?.value ?? undefined) as string | undefined,
      birth_date: owner?.birth_date?.toISOString().split('T')[0] ?? undefined,
      phone_number: owner?.phone_number?.replaceAll(/[\s().+-]/g, '') ?? undefined,
    })),
    industry_volume: industryVolume.success ? industryVolume.data : undefined,
    transaction_modes: transactionModes.success
      ? {
          in_person: transactionModes.data.in_person,
          online: transactionModes.data.online,
        }
      : undefined,
    service_deliveries: serviceDeliveries.success
      ? {
          same_day_delivery: serviceDeliveries.data.same_day_delivery,
          one_week_delivery: serviceDeliveries.data.one_week_delivery,
          two_week_delivery: serviceDeliveries.data.two_week_delivery,
          one_month_delivery: serviceDeliveries.data.one_month_delivery,
          more_than_one_month_delivery: serviceDeliveries.data.more_than_one_month_delivery,
        }
      : undefined,
    plan_id: (params.plan_id ?? params.plan?.id) as PartnerProcessingPlanId | undefined,
    plan: params.plan ? mapPlanFormSchemaToUpdateSchema(params.plan) : undefined,
    sales_code_id: params.sales_code_id,
    sales_agent_id: params.sales_agent_id,
  };
};

export const UpdateMerchantAppSubmitHandler: SubmitHandler<
  MerchantApplicationFormSchema,
  MerchantApplicationRow
> = async (data: MerchantApplicationFormSchema, state?: MerchantApplicationFormState) => {
  'use server';

  try {
    const changedValues = getChangedValues(data, state?.defaultValues, state?.dirtyFields);
    if (state?.dirtyFields.address) {
      (changedValues as MerchantApplicationFormSchema).address = data.address;
    }

    const params = mapApplicationFormSchemaToUpdateSchema(changedValues as MerchantApplicationFormSchema);
    if (!state?.dirtyFields.plan) {
      delete params.plan;
    }

    if (state?.dirtyFields.plan && data.plan) {
      // getChangedValues doesn't diff the array of cards_accepted
      const mappedPlan = mapPlanFormSchemaToUpdateSchema(data.plan);
      params.plan = params.plan ?? ({} as ProcessingPlanFormSchema);
      params.plan.cards_accepted = params.plan.cards_accepted ?? mappedPlan.cards_accepted;
      params.plan.comment = params.plan.comment ?? mappedPlan.comment;
    }

    if (params.company && !state?.dirtyFields.company?.tax_id) {
      delete params.company.tax_id;
    }

    params.owners = params.owners?.map((owner, index) => ({
      ...owner,
      ...(state?.dirtyFields.owners?.[index]?.ssn?.value ? { ssn: owner.ssn } : { ssn: undefined }),
    }));

    const response = await updateMerchantApplicationDraft({
      params: {
        ...params,
        id: data.id as MerchantApplicationId,
      },
    });

    if (response.success) {
      return { success: true as const, data: response.data };
    }

    if (response.error) {
      Sentry.captureException(response.error);
      logger.error('Error updating merchant application', { error: response.error });
      return { success: false as const, error: response.error.body };
    }

    throw new Error('An unknown error occurred');
  } catch (e) {
    logger.error('Error updating merchant application', { error: e });
    Sentry.captureException(e as Error);
    const err = ServerErrorResponse.parse(new ServerError({ cause: e }).toJSON());
    return { success: false as const, error: err };
  }
};
