import 'server-only';

import { TenantTermsAndConditionsDTO } from '@dbd/core-types/domain/tenant/boarding-settings.domain';
import { requireUser } from '@dbd/next-sessions/auth';
import {
  GetMerchantApplicationByIdCommand,
  GetMerchantApplicationsFacetsCommand,
  ListMerchantApplicationsCommand,
  ListMerchantApplicationsMetaCommand,
  StreamMerchantApplicationsCommand,
} from '@dbd/reporting-merchant-apps/merchant-application.commands';
import { ListMerchantApplicationsResponse } from '@dbd/reporting-merchant-apps/merchant-application.dto.js';
import { MerchantApplicationRow } from '@dbd/reporting-merchant-apps/merchant-application.types.js';
import { getAuthenticatedClient, handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { FacetedSearchResponse } from '@dbd/zod-types';
import type { ListMeta, MerchantApplicationId, TenantId } from '@dbd/zod-types-common/constants';
import { ForbiddenError, NotFoundError, ServerError } from '@dbd/zod-types-common/errors';

import { getMerchantApplicationService } from './merchant-application.service.js';

const logger = getLogger('merchant-application.loaders');

export async function getMerchantApplicationById(
  params: Omit<GetMerchantApplicationByIdCommand, 'authUser'>,
): Promise<MerchantApplicationRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getMerchantApplicationService();
    const data = await service.getMerchantApplicationById(
      GetMerchantApplicationByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting merchant application by id', params });
    throw handleNextError(error);
  }
}

export async function listMerchantApplications(
  params: Omit<ListMerchantApplicationsCommand, 'authUser'>,
): Promise<ListMerchantApplicationsResponse> {
  try {
    const user = await requireUser();

    const service = await getMerchantApplicationService();
    const data = await service.listMerchantApplications(
      ListMerchantApplicationsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing merchant applications', params });
    throw handleNextError(error);
  }
}

export async function listMerchantApplicationsMeta(
  params: Omit<ListMerchantApplicationsMetaCommand, 'authUser'>,
): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getMerchantApplicationService();
    const data = await service.listMerchantApplicationsMeta(
      ListMerchantApplicationsMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing merchant applications meta', params });
    throw handleNextError(error);
  }
}

export async function streamMerchantApplications(
  params: Omit<StreamMerchantApplicationsCommand, 'authUser'>,
): Promise<AsyncGenerator<MerchantApplicationRow[]>> {
  try {
    const user = await requireUser();

    const service = await getMerchantApplicationService();
    const data = await service.streamMerchantApplications(
      StreamMerchantApplicationsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming merchant applications', params });
    throw handleNextError(error);
  }
}

export async function getMerchantApplicationFacets(
  params: Omit<GetMerchantApplicationsFacetsCommand, 'authUser'>,
): Promise<FacetedSearchResponse> {
  try {
    const user = await requireUser();

    const service = await getMerchantApplicationService();
    const data = await service.getMerchantApplicationFacets(
      GetMerchantApplicationsFacetsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting merchant application facets', params });
    throw handleNextError(error);
  }
}

export async function getMerchantApplicationTermsAndConditions(params: {
  tenant_id: TenantId;
}): Promise<TenantTermsAndConditionsDTO> {
  const { user, client } = await getAuthenticatedClient();
  if (user.tenant_id !== params.tenant_id) {
    throw new ForbiddenError();
  }

  try {
    const response = await client.tenantBoardingSettings.retrieveTermsAndConditions(params.tenant_id);
    if (!response) {
      throw NotFoundError.fromError(new Error('Terms and conditions not found'));
    }

    return response;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting merchant application terms and conditions', params });
    throw handleNextError(error);
  }
}

export async function getMerchantApplicationDocuments(params: { id: MerchantApplicationId }) {
  const { user, client } = await getAuthenticatedClient();

  try {
    const application = await getMerchantApplicationById({ params: { id: params.id, fields: [] } });
    if (!application) {
      throw new NotFoundError();
    }

    if (application.tenant_id !== user.tenant_id || (user.partner_id && application.partner_id !== user.partner_id)) {
      throw new ForbiddenError();
    }

    const response = await client.files.list({ source_id: application.id });
    if (!response || !response.data) {
      throw new ServerError();
    }

    return response.data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting merchant application documents', params });
    throw handleNextError(error);
  }
}
