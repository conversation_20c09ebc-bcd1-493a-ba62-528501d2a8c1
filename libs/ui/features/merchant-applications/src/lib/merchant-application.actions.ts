'use server';
import 'server-only';

import { type FileUnderwritingDocumentType } from '@dbd/core-types/constants/file.constants';
import { ParentSourceTypeEnum } from '@dbd/core-types/enums/common';
import { FileBusinessPurposeEnum } from '@dbd/core-types/enums/file.enums';
import { requireUser } from '@dbd/next-sessions/auth';
import {
  CreateMerchantApplicationDraftCommand,
  UpdateMerchantApplicationDraftCommand,
} from '@dbd/reporting-merchant-apps/merchant-application.commands';
import { getAuthenticatedClient, handleServiceError, withSentryAction } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { BusinessId, MerchantApplicationId } from '@dbd/zod-types-common';
import { ForbiddenError, NotFoundError, ServerError } from '@dbd/zod-types-common/errors';
import { z } from 'zod';

import { getMerchantApplicationById } from './merchant-application.loaders.js';
import { getMerchantApplicationService } from './merchant-application.service.js';

const logger = getLogger('merchant-application.actions');

export const createMerchantApplicationDraft = withSentryAction(async function createMerchantApplicationDraft(
  params: Omit<CreateMerchantApplicationDraftCommand, 'authUser'>,
) {
  try {
    const user = await requireUser();
    const service = await getMerchantApplicationService();
    const data = await service.createMerchantApplicationDraft(
      CreateMerchantApplicationDraftCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    if (!data) {
      throw new ServerError({ cause: new Error('Application not created') });
    }

    return {
      success: true as const,
      data,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error creating merchant application draft', params });
    return handleServiceError(e);
  }
});

export const updateMerchantApplicationDraft = withSentryAction(async function updateMerchantApplicationDraft(
  params: Omit<UpdateMerchantApplicationDraftCommand, 'authUser'>,
) {
  try {
    const user = await requireUser();

    const service = await getMerchantApplicationService();
    const data = await service.updateMerchantApplicationDraft(
      UpdateMerchantApplicationDraftCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    if (!data) {
      throw new Error('Application not updated');
    }

    return {
      success: true as const,
      data,
    };
  } catch (e) {
    logger.error({ err: e, message: 'Error updating merchant application draft', params });
    return handleServiceError(e);
  }
});

export const generateMerchantApplicationLink = withSentryAction(async function generateMerchantApplicationLink(
  id: MerchantApplicationId,
) {
  try {
    const { user, client } = await getAuthenticatedClient();
    const application = await getMerchantApplicationById({ params: { id, fields: [] } });
    if (!application) {
      throw new NotFoundError();
    }

    if (application.tenant_id !== user.tenant_id || (user.partner_id && application.partner_id !== user.partner_id)) {
      throw new ForbiddenError();
    }

    const link = await client.applications.generateLink(id);
    if (link) {
      return {
        success: true as const,
        data: link,
      };
    }

    throw new Error('Link not found');
  } catch (e) {
    logger.error({ err: e, message: 'Error generating merchant application link', params: { id } });
    return handleServiceError(e);
  }
});

const UploadMerchantApplicationDocumentsParams = z.object({
  businessId: BusinessId,
  applicationId: MerchantApplicationId,
  files: z.array(z.object({ type: z.string(), file: z.instanceof(File) })),
});

export const uploadMerchantApplicationDocuments = withSentryAction(async function uploadMerchantApplicationDocuments(
  params: FormData,
) {
  try {
    const { accessToken } = await getAuthenticatedClient();
    const types = params.getAll('types') as string[] | null;
    const files = params.getAll('files') as File[] | null;
    if (!files || !types) {
      throw new Error('Files and types are required');
    }

    const fileMap = files.map((file, index) => ({ file, type: types[index] as FileUnderwritingDocumentType }));

    const payload = UploadMerchantApplicationDocumentsParams.parse({
      businessId: params.get('businessId') as BusinessId,
      applicationId: params.get('applicationId') as MerchantApplicationId,
      files:
        fileMap?.map(({ file, type }) => ({
          file,
          type,
        })) ?? [],
    });

    const promises = payload.files.map(async ({ file, type }, index) => {
      try {
        const url = `${process.env.FILE_SERVICE_URL}/files`;
        const body = new FormData();
        body.append('file', file);
        body.append('purpose', FileBusinessPurposeEnum.UNDERWRITING_DOCUMENT);
        body.append('source_id', payload.applicationId);
        body.append('source_type', ParentSourceTypeEnum.APPLICATION);
        body.append('owner_id', payload.businessId);
        body.append('document_type', type);

        const response = await fetch(url, {
          method: 'POST',
          body,
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });

        const data = await response.json();
        if (!response.ok) {
          throw new Error('Failed to upload file', { cause: data || response.statusText });
        }

        return {
          name: file.name,
          index,
          success: true as const,
          data,
        };
      } catch (e) {
        return {
          name: file.name,
          index,
          success: false as const,
          error: (e as Error).message,
        };
      }
    });

    const responses = await Promise.all(promises);
    const success = responses.every((response) => response.success);

    if (success) {
      return {
        success: true as const,
        data: responses.map((response) => response.data),
      };
    } else {
      return {
        success: false as const,
        data: responses.map((response) => ({
          name: response.name,
          index: response.index,
          error: response.error,
        })),
      };
    }
  } catch (e) {
    logger.error({ err: e, message: 'Error uploading merchant application documents', params });
    return handleServiceError(e);
  }
});
