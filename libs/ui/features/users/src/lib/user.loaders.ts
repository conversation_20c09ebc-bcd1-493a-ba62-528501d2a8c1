import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  AggregateUsersCommand,
  GetUserByIdCommand,
  ListUsersCommand,
  ListUsersMetaCommand,
  StreamUsersCommand,
} from '@dbd/reporting-users/user.commands';
import { AggregateUsersResponse, ListUsersResponse } from '@dbd/reporting-users/user.dto.js';
import { UserRow } from '@dbd/reporting-users/user.types.js';
import { handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { ListMeta } from '@dbd/zod-types-common';

import { getUserService } from './user.service.js';

const logger = getLogger('user.loaders');
export async function getUserById(params: Omit<GetUserByIdCommand, 'authUser'>): Promise<UserRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getUserService();
    const data = await service.getUserById(
      GetUserByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting user by id', params });
    throw handleNextError(error);
  }
}

export async function listUsers(params: Omit<ListUsersCommand, 'authUser'>): Promise<ListUsersResponse> {
  try {
    const user = await requireUser();

    const service = await getUserService();
    const data = await service.listUsers(
      ListUsersCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing users', params });
    throw handleNextError(error);
  }
}

export async function listUsersMeta(params: Omit<ListUsersMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getUserService();
    const data = await service.listUsersMeta(
      ListUsersMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing users meta', params });
    throw handleNextError(error);
  }
}

export async function aggregateUsers(params: Omit<AggregateUsersCommand, 'authUser'>): Promise<AggregateUsersResponse> {
  try {
    const user = await requireUser();

    const service = await getUserService();
    const data = await service.aggregateUsers(
      AggregateUsersCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error aggregating users', params });
    throw handleNextError(error);
  }
}

export async function streamUsers(params: Omit<StreamUsersCommand, 'authUser'>): Promise<AsyncGenerator<UserRow[]>> {
  try {
    const user = await requireUser();

    const service = await getUserService();
    const data = await service.streamUsers(
      StreamUsersCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming users', params });
    throw handleNextError(error);
  }
}
