import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  AggregatePayoutsCommand,
  GetPayoutByIdCommand,
  GetPayoutFacetsCommand,
  ListPayoutsCommand,
  ListPayoutsMetaCommand,
  StreamPayoutsCommand,
} from '@dbd/reporting-payouts/payout.commands';
import { AggregatePayoutsResponse, ListPayoutsResponse } from '@dbd/reporting-payouts/payout.dto.js';
import { PayoutRow } from '@dbd/reporting-payouts/payout.types.js';
import { handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { FacetedSearchResponse } from '@dbd/zod-types';
import { ListMeta } from '@dbd/zod-types-common';

import { getPayoutService } from './payout.service.js';

const logger = getLogger('payout.loaders');

export async function getPayoutById(params: Omit<GetPayoutByIdCommand, 'authUser'>): Promise<PayoutRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getPayoutService();
    const data = await service.getPayoutById(
      GetPayoutByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting payout by id', params });
    throw handleNextError(error);
  }
}

export async function listPayouts(params: Omit<ListPayoutsCommand, 'authUser'>): Promise<ListPayoutsResponse> {
  try {
    const user = await requireUser();

    const service = await getPayoutService();
    const data = await service.listPayouts(
      ListPayoutsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing payouts', params });
    throw handleNextError(error);
  }
}

export async function listPayoutsMeta(params: Omit<ListPayoutsMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getPayoutService();
    const data = await service.listPayoutsMeta(
      ListPayoutsMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing payouts meta', params });
    throw handleNextError(error);
  }
}

export async function aggregatePayouts(
  params: Omit<AggregatePayoutsCommand, 'authUser'>,
): Promise<AggregatePayoutsResponse> {
  try {
    const user = await requireUser();

    const service = await getPayoutService();
    const data = await service.aggregatePayouts(
      AggregatePayoutsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error aggregating payouts', params });
    throw handleNextError(error);
  }
}

export async function streamPayouts(
  params: Omit<StreamPayoutsCommand, 'authUser'>,
): Promise<AsyncGenerator<PayoutRow[]>> {
  try {
    const user = await requireUser();

    const service = await getPayoutService();
    const data = await service.streamPayouts(
      StreamPayoutsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming payouts', params });
    throw handleNextError(error);
  }
}

export async function getPayoutFacets(
  params: Omit<GetPayoutFacetsCommand, 'authUser'>,
): Promise<FacetedSearchResponse> {
  try {
    const user = await requireUser();

    const service = await getPayoutService();
    const data = await service.getPayoutFacets(
      GetPayoutFacetsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting payout facets', params });
    throw handleNextError(error);
  }
}
