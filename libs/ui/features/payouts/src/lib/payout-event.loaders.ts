import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  AggregatePayoutEventsCommand,
  ListPayoutEventsCommand,
  ListPayoutEventsMetaCommand,
  StreamPayoutEventsCommand,
} from '@dbd/reporting-payout-events/payout-event.commands';
import {
  AggregatePayoutEventsResponse,
  ListPayoutEventsResponse,
} from '@dbd/reporting-payout-events/payout-event.dto.js';
import { PayoutEventRow } from '@dbd/reporting-payout-events/payout-event.types.js';
import { handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { ListMeta } from '@dbd/zod-types-common';

import { getPayoutEventService } from './payout-event.service.js';

const logger = getLogger('payout-event.loaders');

export async function listPayoutEvents(
  params: Omit<ListPayoutEventsCommand, 'authUser'>,
): Promise<ListPayoutEventsResponse> {
  try {
    const user = await requireUser();

    const service = await getPayoutEventService();
    const data = await service.listPayoutEvents(
      ListPayoutEventsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing payout events', params });
    throw handleNextError(error);
  }
}

export async function listPayoutEventsMeta(params: Omit<ListPayoutEventsMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getPayoutEventService();
    const data = await service.listPayoutEventsMeta(
      ListPayoutEventsMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing payout events meta', params });
    throw handleNextError(error);
  }
}

export async function aggregatePayoutEvents(
  params: Omit<AggregatePayoutEventsCommand, 'authUser'>,
): Promise<AggregatePayoutEventsResponse> {
  try {
    const user = await requireUser();

    const service = await getPayoutEventService();
    const data = await service.aggregatePayoutEvents(
      AggregatePayoutEventsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error aggregating payout events', params });
    throw handleNextError(error);
  }
}

export async function streamPayoutEvents(
  params: Omit<StreamPayoutEventsCommand, 'authUser'>,
): Promise<AsyncGenerator<PayoutEventRow[]>> {
  try {
    const user = await requireUser();

    const service = await getPayoutEventService();
    const data = await service.streamPayoutEvents(
      StreamPayoutEventsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming payout events', params });
    throw handleNextError(error);
  }
}
