'use server';
import 'server-only';

import { ReadWriteRoles } from '@dbd/core-types/constants/auth.constants';
import { getAuthenticatedClient, handleServiceError, withSentryAction } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils/logging';
import type { GatewayId, GatewayType } from '@dbd/zod-types';
import { ForbiddenError, type MerchantAccountId, NotFoundError } from '@dbd/zod-types-common';

import { getMerchantAccountById } from './merchant.loaders.js';

const logger = getLogger('merchant.actions');

export type BoardMerchantToGatewayProps = {
  accountId: MerchantAccountId;
  gatewayType: GatewayType;
  gatewayId: GatewayId;
  mid: string;
};
export const boardMerchantToGateway = withSentryAction(async function boardMerchantToGateway(
  params: BoardMerchantToGatewayProps,
) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const merchant = await getMerchantAccountById({
      params: {
        id: params.accountId,
        fields: [],
      },
    });
    if (!merchant) {
      throw new NotFoundError({ cause: new Error(`Merchant not found: ${params.accountId}`) });
    }

    if (user.tenant_id !== merchant.tenant_id || (user.partner_id && user.partner_id !== merchant.partner_id)) {
      throw new ForbiddenError({ cause: new Error(`User is not allowed to board merchant ${params.accountId}`) });
    }

    const hasRole = user.roles.includes(ReadWriteRoles.MERCHANT_GATEWAY_BOARDING_WRITE);
    if (!hasRole) {
      throw new ForbiddenError({ cause: new Error(`User is not allowed to board merchant ${params.accountId}`) });
    }

    const response = await client.boardingGateway.updateGateway(
      {
        account_id: params.mid,
        gateway_id: params.gatewayId,
        gateway_type: params.gatewayType,
      },
      merchant.account_id,
    );
    return { success: true as const, data: response };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error boarding merchant to gateway', params });
    return handleServiceError(e);
  }
});
