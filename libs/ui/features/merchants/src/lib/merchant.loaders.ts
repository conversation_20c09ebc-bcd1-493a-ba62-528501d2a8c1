import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  GetMerchantAccountByIdCommand,
  ListMerchantAccountsCommand,
  ListMerchantAccountsMetaCommand,
  StreamMerchantAccountsCommand,
} from '@dbd/reporting-merchant-accounts/merchant-account.commands';
import { ListMerchantAccountsResponse } from '@dbd/reporting-merchant-accounts/merchant-account.dto.js';
import { MerchantAccountRow } from '@dbd/reporting-merchant-accounts/merchant-account.types.js';
import { fromCents } from '@dbd/ui/lib/formatters';
import { getAuthenticatedClient, handleNextError } from '@dbd/ui/lib/server';
import { ProcessingPlanFormSchema } from '@dbd/ui-partners/schemas/processing-plan-form-schema';
import { getLogger } from '@dbd/utils';
import { OrderDirection } from '@dbd/zod-types';
import type { BusinessId, ListMeta, MerchantAccountId, PartnerId } from '@dbd/zod-types-common/constants';

import { getMerchantAccountService } from './merchant.service.js';

const logger = getLogger('merchant.loaders');

export async function getMerchantAccountById(
  params: Omit<GetMerchantAccountByIdCommand, 'authUser'>,
): Promise<MerchantAccountRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getMerchantAccountService();
    const data = await service.getMerchantAccountById(
      GetMerchantAccountByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting merchant account by id', params });
    throw handleNextError(error);
  }
}

export async function listMerchantAccounts(
  params: Omit<ListMerchantAccountsCommand, 'authUser'>,
): Promise<ListMerchantAccountsResponse> {
  try {
    const user = await requireUser();

    const service = await getMerchantAccountService();
    const data = await service.listMerchantAccounts(
      ListMerchantAccountsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing merchant accounts', params });
    throw handleNextError(error);
  }
}

export async function listMerchantAccountNames(params: Omit<ListMerchantAccountsCommand, 'authUser'>): Promise<{
  data: { account_name: string; account_id: MerchantAccountId; business_id: BusinessId; partner_id: PartnerId }[];
}> {
  try {
    const user = await requireUser();

    const service = await getMerchantAccountService();
    const data = await service.listMerchantAccounts(
      ListMerchantAccountsCommand.parse({
        ...params,
        params: {
          ...params.params,
          fields: ['account_name', 'account_id', 'business_id', 'partner_id'],
          orderBy: [['account_name', OrderDirection.ASC]],
        },
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing merchant account names', params });
    throw handleNextError(error);
  }
}

export async function listMerchantAccountsMeta(
  params: Omit<ListMerchantAccountsMetaCommand, 'authUser'>,
): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getMerchantAccountService();
    const data = await service.listMerchantAccountsMeta(
      ListMerchantAccountsMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing merchant accounts meta', params });
    throw handleNextError(error);
  }
}

export async function streamMerchantAccounts(
  params: Omit<StreamMerchantAccountsCommand, 'authUser'>,
): Promise<AsyncGenerator<MerchantAccountRow[]>> {
  try {
    const user = await requireUser();

    const service = await getMerchantAccountService();
    const data = await service.streamMerchantAccounts(
      StreamMerchantAccountsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming merchant accounts', params });
    throw handleNextError(error);
  }
}

/**
 * Retrieves the current processing plan for a merchant account
 *
 * This function fetches the payment settings for the specified merchant account
 * and extracts the active processing plan based on the current date.
 * It transforms the processing plan data into a format suitable for forms.
 *
 * @param params - Object containing the merchant account ID
 * @param params.id - The ID of the merchant account
 * @returns The current processing plan or undefined if none exists
 */
export async function getMerchantCurrentProcessingPlan(params: {
  id: MerchantAccountId;
}): Promise<ProcessingPlanFormSchema | undefined> {
  try {
    const { client } = await getAuthenticatedClient();

    const plan = await client.paymentSettings.getProcessingPlan(params.id);

    return ProcessingPlanFormSchema.parse({
      id: null,
      type: plan.plan_type,
      interchange_cost: plan.interchange_cost,
      description: '',
      ach_linking_fee_paid_by_partner: plan.fees_paid_by_partner?.ach_linking ?? false,
      batch_fee_paid_by_partner: plan.fees_paid_by_partner?.batch ?? false,
      same_day_ach_bps: plan.bank_processing_plan_settings?.fees?.same_day_ach_bps ?? 0,
      same_day_ach_txn_fee: fromCents(plan.bank_processing_plan_settings?.fees?.same_day_ach_per_transaction ?? 0),
      same_day_ach_max_fee: fromCents(plan.bank_processing_plan_settings?.fees?.same_day_ach_max ?? 0),
      auth_cnp_fee: fromCents(plan.auth_fee.card_not_present ?? 0),
      auth_cp_fee: fromCents(plan.auth_fee.card_present ?? 0),
      application_fee: fromCents(plan.fees.application ?? 0),
      application_fee_paid_by_partner: plan.fees_paid_by_partner?.application ?? false,
      chargeback_fee: fromCents(plan.fees.chargeback ?? 0),
      chargeback_fee_paid_by_partner: plan.fees_paid_by_partner?.chargeback ?? false,
      dda_reject_fee: fromCents(plan.fees.dda_reject ?? 0),
      dda_reject_fee_paid_by_partner: plan.fees_paid_by_partner?.dda_reject ?? false,
      early_cancellation_fee: fromCents(plan.fees.early_cancellation ?? 0),
      early_cancellation_fee_paid_by_partner: plan.fees_paid_by_partner?.early_cancellation ?? false,
      gateway_fee: fromCents(plan.fees.gateway ?? 0),
      gateway_fee_paid_by_partner: plan.fees_paid_by_partner?.gateway ?? false,
      monthly_minimum_fee: fromCents(plan.fees.monthly_minimum ?? 0),
      monthly_minimum_fee_paid_by_partner: plan.fees_paid_by_partner?.monthly_minimum ?? false,
      pci_non_compliance_fee: fromCents(plan.fees.pci_non_compliance ?? 0),
      pci_non_compliance_fee_paid_by_partner: plan.fees_paid_by_partner?.pci_non_compliance ?? false,
      platform_fee: fromCents(plan.fees.platform ?? 0),
      platform_fee_paid_by_partner: plan.fees_paid_by_partner?.platform ?? false,
      regulatory_product_fee: fromCents(plan.fees.regulatory_product ?? 0),
      regulatory_product_fee_paid_by_partner: plan.fees_paid_by_partner?.regulatory_product ?? false,
      retrieval_fee: fromCents(plan.fees.retrieval ?? 0),
      retrieval_fee_paid_by_partner: plan.fees_paid_by_partner?.retrieval ?? false,
      token_fee: fromCents(plan.fees.token ?? 0),
      token_fee_paid_by_partner: plan.fees_paid_by_partner?.token ?? false,
      ach_txn_fee: fromCents(plan.bank_processing_plan_settings?.fees?.ach_per_transaction ?? 0),
      ach_max_fee: fromCents(plan.bank_processing_plan_settings?.fees?.ach_max ?? 0),
      ach_reversal_fee: fromCents(plan.bank_processing_plan_settings?.fees?.reversal_fee ?? 0),
      ach_reject_fee: fromCents(plan.bank_processing_plan_settings?.fees?.reject_fee ?? 0),
      bank_linking_fee: fromCents(plan.bank_processing_plan_settings?.fees?.linking_fee ?? 0),
      batch_fee: fromCents(plan.fees.batch ?? 0),
      ach_enabled: !!plan.bank_processing_plan_settings,
      ach_bps: plan.bank_processing_plan_settings?.fees?.ach_bps ?? 0,
      ach_reject_fee_paid_by_partner: plan.fees_paid_by_partner?.ach_reject ?? false,
      ach_reversal_fee_paid_by_partner: plan.fees_paid_by_partner?.ach_reversal ?? false,

      cards_accepted: plan.accepted_cards.map((card) => ({
        card_type: card.card_type,
        cp_bps: card.discount_fee?.card_present_bps ?? 0,
        cnp_bps: card.discount_fee?.card_not_present_bps ?? 0,
        state: card.state,
      })),
      comment: plan.comment,
    });
  } catch (error) {
    logger.error({ err: error, message: 'Error getting merchant current processing plan', params });
    throw handleNextError(error);
  }
}
