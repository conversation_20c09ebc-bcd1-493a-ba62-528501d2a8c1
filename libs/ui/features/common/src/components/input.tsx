import type { MaskitoOptions } from '@maskito/core';
import { useMaskito } from '@maskito/react';
import { cva, VariantProps } from 'class-variance-authority';
import * as React from 'react';
import { type CurrencyInputProps, default as ReactCurrencyInput } from 'react-currency-input-field';

import { cn } from '../lib/utils.js';
import { Icon, IconName } from './icon.js';

const ImportedReactCurrencyInput = ReactCurrencyInput as unknown as React.ComponentType<CurrencyInputProps>;

const variants = {
  size: {
    // Note: padding here only applies for icons.
    // Input padding is defined in inputVariants.
    sm: 'h-8 px-2.5 text-sm',
    md: 'h-10 px-3 text-md',
    lg: 'h-12 px-4 text-lg',
  },
  file: {
    false: null,
    true: 'px-0 pr-3',
  },
};
export const inputSizeValues = Object.keys(variants.size);

const containerVariants = cva(
  'border-input has-[:focus-within]:ring-ring relative m-0 flex w-full items-center justify-center rounded-md border bg-transparent shadow-sm transition-colors disabled:cursor-not-allowed disabled:opacity-50 has-[:focus-within]:ring-0',
  {
    variants,
    defaultVariants: {
      size: 'md',
    },
  },
);

const inputVariants = cva(
  'absolute inset-0 min-w-0 bg-transparent outline-none placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50 file:h-full file:px-4 file:mr-4 file:p-0 file:rounded-md file:border-0 file:text-sm file:font-semibold file:text-foreground file:bg-border file:hover:bg-border/50 file:hover:border-border/50 file:hover:cursor-pointer',
  {
    variants: {
      size: {
        sm: 'px-3 text-sm',
        md: 'text-md px-3',
        lg: 'px-4 text-lg',
      },
      // Declared here for type-safety, but implemented in compoundVariants.
      leftIcon: {
        true: null,
        false: null,
      },
      rightIcon: {
        true: null,
        false: null,
      },
      file: { false: null, true: 'pl-0 pr-3 hover:cursor-pointer inset-[2px]' },
    },
    compoundVariants: [
      { size: 'sm', leftIcon: true, className: 'pl-8' },
      { size: 'md', leftIcon: true, className: 'pl-10' },
      { size: 'lg', leftIcon: true, className: 'pl-12' },
      { size: 'sm', rightIcon: true, className: 'pr-8' },
      { size: 'md', rightIcon: true, className: 'pr-10' },
      { size: 'lg', rightIcon: true, className: 'pr-12' },
    ],
  },
);

const iconVariants = cva('pointer-events-none text-gray-400', {
  variants: {
    size: {
      sm: 'size-4',
      md: 'size-5',
      lg: 'size-6',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

// --

export type InputProps = Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> &
  VariantProps<typeof containerVariants> & {
    leftIcon?: IconName;
    rightIcon?: IconName;
    children?: React.ReactNode;
    debounceMs?: number;
    inputClasses?: string;
    'data-testid'?: string;
  };

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      size = 'md' as keyof typeof variants.size,
      type,
      leftIcon,
      rightIcon,
      className,
      inputClasses,
      children,
      'data-testid': dataTestId,
      onChange,
      debounceMs,
      ...props
    },
    ref,
  ) => {
    const timer = React.useRef<NodeJS.Timeout | null>(null);
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (debounceMs) {
        if (timer.current) {
          clearTimeout(timer.current);
        }
        timer.current = setTimeout(() => {
          onChange?.(e);
        }, debounceMs);
      } else {
        onChange?.(e);
      }
    };

    React.useEffect(() => {
      return () => {
        if (timer.current) {
          clearTimeout(timer.current);
        }
      };
    }, []);

    return (
      <div className={cn(containerVariants({ size, className, file: type === 'file' }), className)}>
        <input
          type={type}
          className={inputVariants({
            size,
            leftIcon: Boolean(leftIcon) && type !== 'file',
            rightIcon: Boolean(rightIcon) && type !== 'file',
            className: inputClasses,
            file: type === 'file',
          })}
          ref={ref}
          data-testid={dataTestId}
          onChange={handleChange}
          {...props}
        />
        {leftIcon && type !== 'file' && (
          <Icon className={iconVariants({ size, className: 'mr-auto' })} name={leftIcon} />
        )}
        {rightIcon && type !== 'file' && (
          <Icon className={iconVariants({ size, className: 'ml-auto' })} name={rightIcon} />
        )}
        {children}
      </div>
    );
  },
);
Input.displayName = 'Input';

const CurrencyInput = React.forwardRef<
  HTMLInputElement,
  VariantProps<typeof containerVariants> &
    CurrencyInputProps &
    Pick<InputProps, 'leftIcon' | 'rightIcon' | 'children' | 'inputClasses' | 'size'>
>(
  (
    { size = 'md' as keyof typeof variants.size, className, inputClasses, children, leftIcon, rightIcon, ...props },
    ref,
  ) => {
    return (
      <div className={cn(containerVariants({ size, className }), className)}>
        <ImportedReactCurrencyInput
          className={inputVariants({
            size,
            leftIcon: Boolean(leftIcon),
            rightIcon: Boolean(rightIcon),
            className: inputClasses,
          })}
          ref={ref}
          {...props}
        />
        {leftIcon && <Icon className={iconVariants({ size, className: 'mr-auto' })} name={leftIcon} />}
        {rightIcon && <Icon className={iconVariants({ size, className: 'ml-auto' })} name={rightIcon} />}
        {children}
      </div>
    );
  },
);
CurrencyInput.displayName = 'CurrencyInput';

const MaskedInput = React.forwardRef<
  HTMLInputElement,
  VariantProps<typeof containerVariants> & { mask: MaskitoOptions; className?: string } & Pick<
      InputProps,
      'leftIcon' | 'rightIcon' | 'children' | 'inputClasses'
    >
>(
  (
    {
      size = 'md' as keyof typeof variants.size,
      mask,
      className,
      inputClasses,
      children,
      leftIcon,
      rightIcon,
      ...props
    },
    ref,
  ) => {
    const maskito = useMaskito({ options: mask });

    return (
      <div className={cn(containerVariants({ size, className }), className)}>
        <input
          type="text"
          className={inputVariants({
            size,
            leftIcon: Boolean(leftIcon),
            rightIcon: Boolean(rightIcon),
            className: inputClasses,
          })}
          ref={(e) => {
            e && maskito(e);
            if (typeof ref === 'function') {
              ref(e);
            } else if (ref) {
              ref.current = e;
            }
          }}
          {...props}
        />
        {leftIcon && <Icon className={iconVariants({ size, className: 'mr-auto' })} name={leftIcon} />}
        {rightIcon && <Icon className={iconVariants({ size, className: 'ml-auto' })} name={rightIcon} />}
        {children}
      </div>
    );
  },
);
MaskedInput.displayName = 'MaskedInput';

export { CurrencyInput, Input, MaskedInput };
