import 'server-only';

import { auth } from '@dbd/next-sessions/auth';
import { getTokenEncryption } from '@dbd/next-sessions/kms';
import { DBDClient } from '@dbd/service-client-library';
import { ResourceContext } from '@dbd/zod-types-common/common';
import { BusinessId, MerchantAccountId, PartnerId, TenantId } from '@dbd/zod-types-common/constants';
import { isHTTPError, ServerError, UnauthorizedError } from '@dbd/zod-types-common/errors';
import * as Sentry from '@sentry/nextjs';
import flagsmith from 'flagsmith/isomorphic';
import { <PERSON>lagsmith } from 'flagsmith/types';
import { headers } from 'next/headers.js';
import { pino } from 'pino';

import { isNotFoundNavigationError, isRedirectNavigationError, isSessionError } from './utils';

export const logger = pino({
  name: 'ui',
  level: 'info',
});

export const parseResourceContext = (headers: Record<string, string>) => {
  const resourceCtx: ResourceContext = {};

  if (headers['x-account-id']) {
    resourceCtx.account_id = MerchantAccountId.parse(headers['x-account-id']);
  }
  if (headers['x-business-id']) {
    resourceCtx.business_id = BusinessId.parse(headers['x-business-id']);
  }
  if (headers['x-partner-id']) {
    resourceCtx.partner_id = PartnerId.parse(headers['x-partner-id']);
  }
  if (headers['x-tenant-id']) {
    resourceCtx.tenant_id = TenantId.parse(headers['x-tenant-id']);
  }
  return resourceCtx;
};

export const escapeCsv = (value: unknown) => {
  if (value === null) return '';
  if (typeof value === 'string') {
    const val = value.replace(/"/g, '""');
    return val.indexOf(',') !== -1 ? `"${val}"` : val;
  }
  if (typeof value === 'object' && !(value instanceof Date)) {
    return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
  }

  return JSON.stringify(value);
};

export type ExportColumn<T extends Record<string, unknown>> = {
  label: string;
  value: keyof T | ((row: T) => string | number | boolean);
};

export async function* createCsvStream<T extends Record<string, unknown>>(
  stream: AsyncIterable<T[]>,
  keys: ExportColumn<T>[],
) {
  yield keys.map((column) => column.label).join(',') + '\n';

  for await (const chunk of stream) {
    for (const row of chunk) {
      yield keys
        .map((column) => {
          const val = typeof column.value === 'function' ? column.value(row) : row[column.value];
          if (val === null) return '';
          return escapeCsv(val);
        })
        .join(',') + '\n';
    }
  }
}

export const handleServiceError = (e: unknown) => {
  const error = handleNextError(e);
  if (isHTTPError(error)) {
    return {
      success: false as const,
      error: error.toResponse(),
    };
  }

  throw error;
};

export const getAuthenticatedClient = async () => {
  const { user, session } = await auth();
  if (!user || !session) {
    throw new UnauthorizedError({ cause: new Error('User could not be authenticated') });
  }

  if (user.status !== 'ACTIVE') {
    throw new UnauthorizedError();
  }

  const { decrypt } = await getTokenEncryption();
  const accessToken = await decrypt(session.accessToken, session.dataKey, Buffer.from(session.id));
  if (!accessToken) {
    throw new Error('Failed to decrypt access token');
  }

  const client = new DBDClient(accessToken);

  return {
    user,
    session,
    client,
    accessToken,
  };
};

export const getRetoolConfig = () => {
  const RETOOL_ADMINS_GROUP = 6;
  const TENANT_ENVIRONMENT: Record<string, string> = JSON.parse(process.env.RETOOL_TENANT_ENV || '{}');
  // ex. { "tnt_1231": "sandbox:fiserv" }
  return {
    embedUrl: `${process.env.RETOOL_URL}/api/embed-url/external-user`,
    env: TENANT_ENVIRONMENT,
    adminGroups: process.env.RETOOL_ADMIN_GROUPS
      ? process.env.RETOOL_ADMIN_GROUPS.split(',').map((i) => parseInt(i))
      : [RETOOL_ADMINS_GROUP],
    apps: {
      tenantUsers: process.env.RETOOL_TENANT_USERS_APP_ID ?? '5b37195c-3ce9-11ee-8ee6-1bad627a9c03',
      nextGenReports: process.env.RETOOL_NEXT_GEN_REPORTS_APP_ID ?? '4316ee94-a9ac-11ee-ba03-339ef4a01586',
      residualReports: process.env.RETOOL_RESIDUAL_REPORTS_APP_ID ?? '4c0be852-ebb9-11ee-ad98-13ee9976d0b3',
      merchantReports: process.env.RETOOL_MERCHANT_REPORTS_APP_ID ?? '73e25e0c-c12c-4f00-9151-4fdb1017c2cb',
    },
    emailDomain: 'getfwd.com',
    apiKey: process.env.RETOOL_API_KEY,
  };
};

export const getRetoolEmbedUrl = async (
  app: string,
  metadata: Record<string, unknown> = {},
): Promise<{ embedUrl: string; error: boolean; message?: string }> => {
  const { user, session } = await auth();
  if (!user || !session) {
    throw new UnauthorizedError();
  }
  const { decrypt } = await getTokenEncryption();
  const accessToken = await decrypt(session.accessToken, session.dataKey, Buffer.from(session.id));
  if (!accessToken) {
    throw new Error('Failed to decrypt access token');
  }
  const config = getRetoolConfig();
  const retoolHeaders = new Headers();
  retoolHeaders.append('Content-Type', 'application/json');
  retoolHeaders.append('Authorization', `Bearer ${process.env.RETOOL_API_KEY}`);
  const params = {
    landingPageUuid: app,
    groupIds: config.adminGroups,
    externalIdentifier: `${session.tenantId}`,
    userInfo: {
      firstName: `PFAC (${session.user?.first_name} ${session.user?.last_name})`,
      lastName: 'Admin',
      email: `${session.tenantId}@${config.emailDomain}`,
    },
    environment: config.env[session.tenantId ?? ''],
    metadata: {
      ...metadata,
      embed: true,
      ...session.user,
      ...{ accessToken: accessToken },
    },
  };
  const raw = JSON.stringify(params);

  const requestOptions = {
    method: 'POST',
    headers: retoolHeaders,
    body: raw,
    cache: 'no-cache',
  } as const;

  const response = await fetch(config.embedUrl, requestOptions);
  const resp = await response.json();
  // Replace http with https
  // Needed to make this nullsafe because it would break app functionality for user if they clicked on this tab
  resp.embedUrl = resp.embedUrl || '';
  if (resp.embedUrl) {
    resp.error = false;
  }
  return resp;
};

export const handleNextError = (e: unknown): Error => {
  if (isNotFoundNavigationError(e) || isRedirectNavigationError(e)) {
    return e;
  }

  if (isSessionError(e)) {
    return e;
  }

  Sentry.captureException(e);

  if (isHTTPError(e)) {
    return e;
  }

  return new ServerError({ cause: e });
};

export const withSentry =
  <T extends (...args: any[]) => any>(fn: T) =>
  async (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> => {
    try {
      return await fn(...args);
    } catch (e) {
      Sentry.captureException(e);
      throw e;
    }
  };

export const withSentryAction =
  <T extends (...args: any[]) => any>(fn: T) =>
  async (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> =>
    await Sentry.withServerActionInstrumentation(
      fn.name,
      {
        ...(args[0] instanceof FormData ? { formData: args[0] } : {}),
        headers: headers(),
        recordResponse: true,
      },
      () => fn(...args),
    );

export const getFlagsmithClient = async (): Promise<IFlagsmith> => {
  const flagsmithUrl = process.env.FLAGSMITH_API_URL as string;
  const flagsmithEnvironment = process.env.FLAGSMITH_ENVIRONMENT_ID as string;
  const { user } = await auth();

  try {
    await flagsmith.init({
      environmentID: flagsmithEnvironment,
      api: flagsmithUrl,
      identity: user?.id,
      traits: {
        id: user?.id ?? null,
        enterprise_id: user?.enterprise_id ?? null,
        tenant_id: user?.tenant_id ?? null,
        partner_id: user?.partner_id ?? null,
        business_id: user?.business_id ?? null,
        self_id: user?.id ?? null,
        self_subtype: user?.type ?? null,
      },
    });
  } catch (e) {
    logger.error({ err: e }, 'Error initializing flagsmith');
  }

  return flagsmith;
};
