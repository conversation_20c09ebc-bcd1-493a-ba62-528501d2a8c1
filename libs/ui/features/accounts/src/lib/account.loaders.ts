import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  GetAccountFacetsCommand,
  ListAccountsCommand,
  StreamAccountsCommand,
} from '@dbd/reporting-accounts/account.commands';
import { ListAccountsResponse } from '@dbd/reporting-accounts/account.dto.js';
import { AccountRow } from '@dbd/reporting-accounts/account.types.js';
import { getRetoolConfig, getRetoolEmbedUrl, handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { FacetedSearchResponse } from '@dbd/zod-types';

import { getAccountService } from './account.service.js';

const logger = getLogger('account.loaders');

export async function listAccounts(params: Omit<ListAccountsCommand, 'authUser'>): Promise<ListAccountsResponse> {
  try {
    const user = await requireUser();
    const service = await getAccountService();
    const data = await service.listAccounts(
      ListAccountsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing accounts', params });
    throw handleNextError(error);
  }
}

export async function streamAccounts(
  params: Omit<StreamAccountsCommand, 'authUser'>,
): Promise<AsyncGenerator<AccountRow[]>> {
  try {
    const user = await requireUser();
    const service = await getAccountService();
    const data = await service.streamAccounts(
      StreamAccountsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming accounts', params });
    throw handleNextError(error);
  }
}

export async function getAccountFacets(
  params: Omit<GetAccountFacetsCommand, 'authUser'>,
): Promise<FacetedSearchResponse> {
  try {
    const user = await requireUser();
    const service = await getAccountService();
    const data = await service.getAccountFacets(
      GetAccountFacetsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting account facets', params });
    throw handleNextError(error);
  }
}

export async function getReportsDashboardUrl(metadata: Record<string, unknown> = {}) {
  try {
    const config = getRetoolConfig();
    return await getRetoolEmbedUrl(config.apps.merchantReports, metadata);
  } catch (error) {
    logger.error({ err: error, message: 'Error getting reports dashboard url', params: { metadata } });
    throw handleNextError(error);
  }
}
