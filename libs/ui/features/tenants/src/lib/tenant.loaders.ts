import 'server-only';

import { TenantBoardingSettings } from '@dbd/core-types/domain/tenant/boarding-settings.domain';
import { DBDClient } from '@dbd/service-client-library';
import { getAuthenticatedClient, handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import type { TenantId } from '@dbd/zod-types-common/constants';
import { ForbiddenError } from '@dbd/zod-types-common/errors';

const logger = getLogger('tenant.loaders');

export async function getTenantSettings(params: { tenant_id: TenantId }): Promise<TenantBoardingSettings> {
  try {
    const { user } = await getAuthenticatedClient();
    if (user.tenant_id !== params.tenant_id) {
      throw new ForbiddenError();
    }

    const serviceClient = new DBDClient();

    const response = await serviceClient.tenantBoardingSettings.retrieve(params.tenant_id);
    if (response) {
      return response;
    }

    throw new Error('Tenant settings not found');
  } catch (error) {
    logger.error({ err: error, message: 'Error getting tenant settings', params });
    throw handleNextError(error);
  }
}
