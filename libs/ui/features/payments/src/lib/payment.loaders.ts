import 'server-only';

import { requireUser } from '@dbd/next-sessions/auth';
import {
  AggregatePaymentsCommand,
  GetPaymentByIdCommand,
  GetPaymentFacetsCommand,
  ListPaymentsCommand,
  ListPaymentsMetaCommand,
  StreamPaymentsCommand,
} from '@dbd/reporting-payments/payment.commands';
import { AggregatePaymentsResponse, ListPaymentsResponse } from '@dbd/reporting-payments/payment.dto.js';
import { PaymentRow } from '@dbd/reporting-payments/payment.types.js';
import { handleNextError } from '@dbd/ui/lib/server';
import { getLogger } from '@dbd/utils';
import { FacetedSearchResponse } from '@dbd/zod-types';
import { ListMeta } from '@dbd/zod-types-common';

import { getPaymentService } from './payment.service.js';

const logger = getLogger('payment.loaders');

export async function getPaymentById(params: Omit<GetPaymentByIdCommand, 'authUser'>): Promise<PaymentRow | undefined> {
  try {
    const user = await requireUser();

    const service = await getPaymentService();
    const data = await service.getPaymentById(
      GetPaymentByIdCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting payment by id', params });
    throw handleNextError(error);
  }
}

export async function listPayments(params: Omit<ListPaymentsCommand, 'authUser'>): Promise<ListPaymentsResponse> {
  try {
    const user = await requireUser();

    const service = await getPaymentService();
    const data = await service.listPayments(
      ListPaymentsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing payments', params });
    throw handleNextError(error);
  }
}

export async function listPaymentsMeta(params: Omit<ListPaymentsMetaCommand, 'authUser'>): Promise<ListMeta> {
  try {
    const user = await requireUser();

    const service = await getPaymentService();
    const data = await service.listPaymentsMeta(
      ListPaymentsMetaCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error listing payments meta', params });
    throw handleNextError(error);
  }
}

export async function aggregatePayments(
  params: Omit<AggregatePaymentsCommand, 'authUser'>,
): Promise<AggregatePaymentsResponse> {
  try {
    const user = await requireUser();

    const service = await getPaymentService();
    const data = await service.aggregatePayments(
      AggregatePaymentsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error aggregating payments', params });
    throw handleNextError(error);
  }
}

export async function streamPayments(
  params: Omit<StreamPaymentsCommand, 'authUser'>,
): Promise<AsyncGenerator<PaymentRow[]>> {
  try {
    const user = await requireUser();

    const service = await getPaymentService();
    const data = await service.streamPayments(
      StreamPaymentsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error streaming payments', params });
    throw handleNextError(error);
  }
}

export async function getPaymentFacets(
  params: Omit<GetPaymentFacetsCommand, 'authUser'>,
): Promise<FacetedSearchResponse> {
  try {
    const user = await requireUser();

    const service = await getPaymentService();
    const data = await service.getPaymentFacets(
      GetPaymentFacetsCommand.parse({
        ...params,
        authUser: user,
      }),
    );

    return data;
  } catch (error) {
    logger.error({ err: error, message: 'Error getting payment facets', params });
    throw handleNextError(error);
  }
}
