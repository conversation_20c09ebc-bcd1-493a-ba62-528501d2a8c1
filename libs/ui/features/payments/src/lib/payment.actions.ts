'use server';

import { FEATURE_FLAGS } from '@dbd/flagsmith/constants.js';
import { getAuthenticatedClient, getFlagsmithClient, handleServiceError, withSentryAction } from '@dbd/ui/lib/server';
import { hasGroups } from '@dbd/ui/lib/user';
import { getLogger } from '@dbd/utils/logging';
import {
  BadRequestError,
  ForbiddenError,
  NotFoundError,
  NotImplementedError,
  PaymentId,
  ServerError,
} from '@dbd/zod-types-common';

import { getPaymentById } from './payment.loaders.js';
import { calculateRefundSplits } from './payment.utils.js';

const logger = getLogger('payment.actions');

export const cancelPaymentAction = withSentryAction(async function voidPaymentAction(params: {
  paymentId: PaymentId;
  idempotencyKey?: string;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const payment = await getPaymentById({
      params: {
        id: params.paymentId,
        fields: [],
      },
    });
    if (!payment) {
      throw new NotFoundError({ cause: new Error(`Payment not found: ${params.paymentId}`) });
    }

    if (payment.payment_status !== 'uncaptured') {
      throw new BadRequestError({ cause: new Error('Payment is not voidable') });
    }

    const isAdmin = hasGroups(user, ['BUSINESS_ADMIN']);
    if (payment.business_id !== user.business_id || !isAdmin) {
      throw new ForbiddenError({ cause: new Error('User is not authorized to void this payment') });
    }

    const response = await client.paymentIntent.cancel(
      payment.payment_intent_id,
      {
        accountId: payment.account_id,
      },
      params.idempotencyKey,
    );

    if (!response) {
      throw new ServerError({ cause: new Error('Payment was not voided') });
    }

    // This is a hack because of the propagation delay between DynamoDB and the warehouse
    const waitForStateChange = async () => {
      for (let i = 0; i < 10; i++) {
        const newPayment = await getPaymentById({
          params: {
            id: params.paymentId,
            fields: [],
          },
        });

        if (newPayment?.payment_status === 'cancelled') {
          return newPayment;
        }

        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      throw new ServerError({ cause: new Error('Refund state did not change after 10 seconds') });
    };

    const newPayment = await waitForStateChange();
    if (!newPayment) {
      throw new ServerError({ cause: new Error('Refund was not found') });
    }

    return { success: true as const, data: newPayment };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error voiding payment', params });
    return handleServiceError(e);
  }
});

export const refundPaymentAction = withSentryAction(async function refundPaymentAction(params: {
  paymentId: PaymentId;
  amount: number;
  idempotencyKey?: string;
}) {
  try {
    const { client, user } = await getAuthenticatedClient();
    const flagsmithClient = await getFlagsmithClient();
    const enableRefunds = await flagsmithClient.hasFeature(FEATURE_FLAGS.rollout_merchant_portal_v2_refunds.name);
    if (!enableRefunds) {
      throw new NotImplementedError({ cause: new Error('Refunds are not enabled') });
    }

    const payment = await getPaymentById({
      params: {
        id: params.paymentId,
        fields: [],
      },
    });
    if (!payment) {
      throw new NotFoundError({ cause: new Error('Payment not found') });
    }

    if (payment.business_id !== user.business_id || !hasGroups(user, ['BUSINESS_ADMIN'])) {
      throw new ForbiddenError({ cause: new Error('User is not authorized to refund this payment') });
    }

    if (payment.payment_status !== 'captured') {
      throw new BadRequestError({ cause: new Error('Payment is not refundable') });
    }

    const paymentIntent = await client.paymentIntent.retrieve(payment.payment_intent_id, {
      accountId: payment.account_id,
    });
    if (!paymentIntent) {
      throw new NotFoundError({ cause: new Error('Payment intent not found') });
    }

    const refundableAmount = paymentIntent.amount_captured - paymentIntent.amount_refunded;
    if (params.amount > refundableAmount) {
      throw new BadRequestError({ cause: new Error('Refund amount is greater than the refundable amount') });
    }

    // Contractor splits not supported, remapping them to the partner account
    const refundSplits = calculateRefundSplits(paymentIntent.payment_splits, params.amount).map((split) => ({
      ...split,
      account_id: split.account_id.startsWith('ctrct_') ? payment.partner_id : split.account_id,
      description: [
        `Refund: ${paymentIntent.id}`,
        paymentIntent.description ? `Description: ${paymentIntent.description}` : undefined,
        paymentIntent.reference_id ? `Reference ID: ${paymentIntent.reference_id}` : undefined,
      ]
        .filter(Boolean)
        .join(' - '),
    }));

    const response = await client.paymentIntent.refund(
      {
        id: payment.payment_intent_id,
        amount: params.amount,
        refund_splits: refundSplits,
      },
      { accountId: payment.account_id },
      params.idempotencyKey,
    );

    if (!response) {
      throw new BadRequestError({ cause: new Error('Refund was not created') });
    }

    // This is a hack because of the propagation delay between DynamoDB and the warehouse
    const waitForStateChange = async () => {
      for (let i = 0; i < 10; i++) {
        const newPayment = await getPaymentById({
          params: {
            id: params.paymentId,
            fields: [],
          },
        });

        if (newPayment?.refunded_at_ts) {
          return newPayment;
        }

        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      throw new ServerError({ cause: new Error('Refund state did not change after 10 seconds') });
    };

    const newPayment = await waitForStateChange();
    if (!newPayment) {
      throw new ServerError({ cause: new Error('Refund was not found') });
    }

    return { success: true as const, data: newPayment };
  } catch (e: unknown) {
    logger.error({ err: e, message: 'Error refunding payment', params });
    return handleServiceError(e);
  }
});
