import 'zod-openapi/extend';

import { ApplicationError, BaseErrorValidator } from '@dbd/errors';
import { z } from 'zod';

export const UnauthorizedResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Unauthorized'),
    detail: z.string().default('Access token not set or invalid, and the requested resource could not be returned'),
    status: z.literal(401).default(401),
    code: z.literal('401-01').default('401-01'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'UnauthorizedResponse',
    description: 'Unauthorized response',
  });
export type UnauthorizedResponse = z.infer<typeof UnauthorizedResponse>;

export const ForbiddenResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Forbidden'),
    detail: z.string().default('The resource could not be returned as the requestor is not authorized'),
    status: z.literal(403).default(403),
    code: z.literal('403-01').default('403-01'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'ForbiddenResponse',
    description: 'Forbidden response',
  });
export type ForbiddenResponse = z.infer<typeof ForbiddenResponse>;

export const ServerErrorResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Server Error'),
    detail: z.string().default('The server encountered an unexpected error'),
    status: z.literal(500).default(500),
    code: z.literal('500-01').default('500-01'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'ServerErrorResponse',
    description: 'Server Error response',
  });
export type ServerErrorResponse = z.infer<typeof ServerErrorResponse>;

export const BadRequestResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Bad Request'),
    detail: z.string().default('The request is invalid or malformed'),
    status: z.literal(400).default(400),
    code: z.literal('400-01').default('400-01'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'BadRequestResponse',
    description: 'Bad Request response',
  });
export type BadRequestResponse = z.infer<typeof BadRequestResponse>;

export const InvalidBodyResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Invalid Body'),
    detail: z.string().default('The request contains an invalid body property value.'),
    status: z.literal(400).default(400),
    code: z.literal('400-02').default('400-02'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'InvalidBodyResponse',
    description: 'Invalid Body response',
  });
export type InvalidBodyResponse = z.infer<typeof InvalidBodyResponse>;

export const InvalidQueryResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Invalid Query'),
    detail: z.string().default('The request contains an invalid query property value.'),
    status: z.literal(400).default(400),
    code: z.literal('400-03').default('400-03'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'InvalidQueryResponse',
    description: 'Invalid Query response',
  });
export type InvalidQueryResponse = z.infer<typeof InvalidQueryResponse>;

export const InvalidHeaderResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Invalid Header'),
    detail: z.string().default('The request contains an invalid header property value.'),
    status: z.literal(400).default(400),
    code: z.literal('400-04').default('400-04'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'InvalidHeaderResponse',
    description: 'Invalid Header response',
  });
export type InvalidHeaderResponse = z.infer<typeof InvalidHeaderResponse>;

export const InvalidParamResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Invalid Parameter'),
    detail: z.string().default('The request contains an invalid parameter property value.'),
    status: z.literal(400).default(400),
    code: z.literal('400-05').default('400-05'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'InvalidParamResponse',
    description: 'Invalid Parameter response',
  });
export type InvalidParamResponse = z.infer<typeof InvalidParamResponse>;

export const NotFoundResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Not Found'),
    detail: z.string().default('The requested resource could not be found'),
    status: z.literal(404).default(404),
    code: z.literal('404-01').default('404-01'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'NotFoundResponse',
    description: 'Not Found response',
  });
export type NotFoundResponse = z.infer<typeof NotFoundResponse>;

export const NotImplementedResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Not Implemented'),
    detail: z.string().default('The requested resource is not implemented'),
    status: z.literal(501).default(501),
    code: z.literal('501-01').default('501-01'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'NotImplementedResponse',
    description: 'Not Implemented response',
  });
export type NotImplementedResponse = z.infer<typeof NotImplementedResponse>;

export const ConflictResponse = BaseErrorValidator.strip()
  .extend({
    type: z.literal('about:blank').default('about:blank'),
    title: z.string().default('Conflict'),
    detail: z.string().default('The request resulted in a conflict.'),
    status: z.literal(409).default(409),
    code: z.literal('409-01').default('409-01'),
    instance: z.string().default('Unknown'),
  })
  .openapi({
    ref: 'ConflictResponse',
    description: 'Conditional Check Failed response',
  });
export type ConflictResponse = z.infer<typeof ConflictResponse>;

export class UnauthorizedError extends ApplicationError<UnauthorizedResponse> {
  constructor(params?: (Partial<UnauthorizedResponse> & { cause?: unknown }) | undefined) {
    const cause = UnauthorizedResponse.parse(params ?? {});
    super('Unauthorized' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new UnauthorizedError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 401 as const,
      body: UnauthorizedResponse.parse(this.toJSON()),
    };
  }
}

export class ForbiddenError extends ApplicationError<ForbiddenResponse> {
  constructor(params?: (Partial<ForbiddenResponse> & { cause?: unknown }) | undefined) {
    const cause = ForbiddenResponse.parse(params ?? {});
    super('Forbidden' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new ForbiddenError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 403 as const,
      body: ForbiddenResponse.parse(this.toJSON()),
    };
  }
}

export class BadRequestError extends ApplicationError<BadRequestResponse> {
  constructor(params?: (Partial<BadRequestResponse> & { cause?: unknown }) | undefined) {
    const cause = BadRequestResponse.parse(params ?? {});
    super('BadRequest' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new BadRequestError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 400 as const,
      body: BadRequestResponse.parse(this.toJSON()),
    };
  }
}

export class InvalidBodyError extends ApplicationError<InvalidBodyResponse> {
  constructor(params?: (Partial<InvalidBodyResponse> & { cause?: unknown }) | undefined) {
    const cause = InvalidBodyResponse.parse(params ?? {});
    super('Invalid Body' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new InvalidBodyError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 400 as const,
      body: InvalidBodyResponse.parse(this.toJSON()),
    };
  }
}

export class InvalidQueryError extends ApplicationError<InvalidQueryResponse> {
  constructor(params?: (Partial<InvalidQueryResponse> & { cause?: unknown }) | undefined) {
    const cause = InvalidQueryResponse.parse(params ?? {});
    super('Invalid Query' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new InvalidQueryError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 400 as const,
      body: InvalidQueryResponse.parse(this.toJSON()),
    };
  }
}

export class InvalidHeaderError extends ApplicationError<InvalidHeaderResponse> {
  constructor(params?: (Partial<InvalidHeaderResponse> & { cause?: unknown }) | undefined) {
    const cause = InvalidHeaderResponse.parse(params ?? {});
    super('Invalid Header' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new InvalidHeaderError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 400 as const,
      body: InvalidHeaderResponse.parse(this.toJSON()),
    };
  }
}

export class InvalidParamError extends ApplicationError<InvalidParamResponse> {
  constructor(params?: (Partial<InvalidParamResponse> & { cause?: unknown }) | undefined) {
    const cause = InvalidParamResponse.parse(params ?? {});
    super('Invalid Parameter' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new InvalidParamError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 400 as const,
      body: InvalidParamResponse.parse(this.toJSON()),
    };
  }
}

export class ServerError extends ApplicationError<ServerErrorResponse> {
  constructor(params?: (Partial<ServerErrorResponse> & { cause?: unknown }) | undefined) {
    const cause = ServerErrorResponse.parse(params ?? {});
    super('Server Error' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new ServerError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 500 as const,
      body: ServerErrorResponse.parse(this.toJSON()),
    };
  }
}

export class NotFoundError extends ApplicationError<NotFoundResponse> {
  constructor(params?: (Partial<NotFoundResponse> & { cause?: unknown }) | undefined) {
    const cause = NotFoundResponse.parse(params ?? {});
    super('Not Found' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new NotFoundError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 404 as const,
      body: NotFoundResponse.parse(this.toJSON()),
    };
  }
}

export class NotImplementedError extends ApplicationError<NotImplementedResponse> {
  constructor(params?: (Partial<NotImplementedResponse> & { cause?: unknown }) | undefined) {
    const cause = NotImplementedResponse.parse(params ?? {});
    super('Not Implemented' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new NotImplementedError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 501 as const,
      body: NotImplementedResponse.parse(this.toJSON()),
    };
  }
}

export class ConflictError extends ApplicationError<ConflictResponse> {
  constructor(params?: (Partial<ConflictResponse> & { cause?: unknown }) | undefined) {
    const cause = ConflictResponse.parse(params ?? {});
    super('Conflict' as const, { cause });
    this.cause = params?.cause;
  }

  static fromError(error: Error) {
    return new ConflictError({ cause: error, detail: error.message });
  }

  toResponse() {
    return {
      status: 409 as const,
      body: ConflictResponse.parse(this.toJSON()),
    };
  }
}

export const DefaultResponses = {
  400: z.union([
    BadRequestResponse,
    InvalidBodyResponse,
    InvalidQueryResponse,
    InvalidHeaderResponse,
    InvalidParamResponse,
  ]),
  401: UnauthorizedResponse,
  403: ForbiddenResponse,
  404: NotFoundResponse,
  409: ConflictResponse,
  500: ServerErrorResponse,
  501: NotImplementedResponse,
};

export const isHTTPError = (
  error: unknown,
): error is
  | UnauthorizedError
  | ForbiddenError
  | NotFoundError
  | ConflictError
  | ServerError
  | BadRequestError
  | InvalidBodyError
  | InvalidQueryError
  | InvalidHeaderError
  | InvalidParamError
  | NotImplementedError => {
  return (
    error instanceof UnauthorizedError ||
    error instanceof ForbiddenError ||
    error instanceof NotFoundError ||
    error instanceof ConflictError ||
    error instanceof ServerError ||
    error instanceof BadRequestError ||
    error instanceof InvalidBodyError ||
    error instanceof InvalidQueryError ||
    error instanceof InvalidHeaderError ||
    error instanceof InvalidParamError ||
    error instanceof NotImplementedError
  );
};
