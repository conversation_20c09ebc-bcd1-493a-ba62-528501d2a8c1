/**
 * Notes these exports are provided for public callers
 * to avoid circular dependencies as repos transition to
 * the usage of standard package.json export declarations
 *
 * The modules which export these entities are:
 * - libs/risk/src/modules/entities/risk-profile/credit-risk/credit-risk.dto.ts
 * - libs/risk/src/modules/entities/risk-profile/credit-risk/credit-risk.entity.ts
 * - libs/risk/src/modules/entities/risk-profile/merchant-account/merchant-account.dto.ts
 * - libs/risk/src/modules/entities/risk-profile/merchant-account/merchant-account.entity.ts
 * - libs/risk/src/modules/entities/risk-profile/merchant-limit/merchant-limit.dto.ts
 * - libs/risk/src/modules/entities/risk-profile/merchant-limit/merchant-limit.entity.ts
 * - libs/risk/src/modules/entities/risk-profile/risk-profile.dto.ts
 */
import { z } from 'zod';

export const AccountIDRequest = z.string().startsWith('acct');
export type AccountIDRequest = z.infer<typeof AccountIDRequest>;

const RiskLevels = z.enum(['EXTRA_HIGH_RISK', 'HIGH_RISK', 'MODERATE_RISK', 'LOW_RISK', 'NONE']);
const MonitoringPrograms = z.string();

const accountId = z.string().startsWith('acct_', { message: 'a valid merchant account ID is required' });

// This value translate to $42,949,672.95USD when converting from cents to
// dollars. It just so happens to be the unsigned INT limit for a MySQL column:
// https://dev.mysql.com/doc/refman/8.4/en/integer-types.html
const AcceptableLimitValue = z.number().min(0).max(**********).int();

const OneRatio = z.number().nonnegative().max(1, 'Number must be less than or equal to 1');

const NullableRiskValue = z
  .preprocess((val) => {
    if (typeof val === 'string') {
      let result = '';
      let lastCharWasSpace = false;

      for (let i = 0; i < val.length; i++) {
        const char = val[i];
        if (char === ' ' || char === '-') {
          lastCharWasSpace = true;
        } else {
          if (lastCharWasSpace && result.length > 0) {
            result += '_';
          }
          result += char.toUpperCase();
          lastCharWasSpace = false;
        }
      }
      return result;
    }
    return val;
  }, RiskLevels)
  .nullish();

export const AccountRiskElementCreateRequestDTO = z.object({
  account_id: accountId,
  // Every field is optional (for now)
  aml_fraud_risk: NullableRiskValue,
  enhanced_monitoring: z.boolean().nullish(),
  excessive_program: z.boolean().nullish(),
  fraud_score: z.number().nullish(),
  mcc_category_risk: NullableRiskValue,
  monitoring_programs: z.array(MonitoringPrograms).default([]),
  udaap_risk: NullableRiskValue,
});

export type AccountRiskElementCreateRequestDTO = z.infer<typeof AccountRiskElementCreateRequestDTO>;

export const AccountLimitElementCreateRequestDTO = z.object({
  account_id: accountId,
  average_ticket_limit: AcceptableLimitValue,
  high_ticket_limit: AcceptableLimitValue,
  annual_volume_limit: AcceptableLimitValue,
  delivery_days_limit: z.number().positive().max(365).safe(),
  cnp_ratio_limit: OneRatio,
});

export type AccountLimitElementCreateRequestDTO = z.infer<typeof AccountLimitElementCreateRequestDTO>;

export const CreditRiskElementCreateRequestDTO = z.object({
  account_id: accountId,
  // Every field is optional (for now)
  account_limits: z.record(z.unknown()).nullish(),
  annual_volume_risk: NullableRiskValue,
  avg_ticket_risk: NullableRiskValue,
  baseline_risk_exposure: z.number().nullish(),
  chargeback_rate: OneRatio.nullish(),
  chargeback_risk_exposure: z.number().nullish(),
  chargeback_risk: NullableRiskValue,
  credit_risk_decision: z.string().nullish(),
  credit_risk_reasons: z.array(z.unknown()).nullish(),
  credit_risk: NullableRiskValue,
  delivery_days: z.number().nullish(),
  non_delivery_risk_exposure: z.number().nullish(),
  non_delivery_risk: NullableRiskValue,
  owner_credit_score: z.number().nullish(),
  partner_risk_factor: z.number().nullish(),
  primarily_online: z.boolean().nullish(),
  refund_rate: OneRatio.nullish(),
  refund_risk_exposure: z.number().nullish(),
  refund_risk: NullableRiskValue,
  review_flags: z.array(z.unknown()).nullish(),
  total_risk_exposure: z.number().nullish(),
});

export type CreditRiskElementCreateRequestDTO = z.infer<typeof CreditRiskElementCreateRequestDTO>;

export const CreditRiskElementResponse = z.object({
  // Identifiers
  id: z.string().startsWith('cre_'),
  account_id: AccountIDRequest,
  // Data
  annual_volume_risk: z.string().nullable(),
  avg_ticket_risk: z.string().nullable(),
  baseline_risk_exposure: z.number().nullish(),
  chargeback_risk_exposure: z.number().nullish(),
  chargeback_risk: z.string().nullable(),
  credit_risk_score: z.number().nullish(),
  credit_risk: z.string().nullable(),
  non_delivery_risk_exposure: z.number().nullish(),
  non_delivery_risk: z.string().nullable(),
  partner_risk_factor: z.number().nullish(),
  refund_rate: z.number().nullish(),
  refund_risk_exposure: z.number().nullish(),
  refund_risk: z.string().nullable(),
  second_sign_occurence: z.boolean().nullish(),
  total_risk_exposure: z.number().nullish(),
  // Standard Timestamps
  created_at: z.union([z.date(), z.string()]).nullish(),
  removed_at: z.union([z.date(), z.string()]).nullish(),
  updated_at: z.union([z.date(), z.string()]).nullish(),
});

export type CreditRiskElementResponse = z.infer<typeof CreditRiskElementResponse>;

export const AccountRiskElementResponse = z.object({
  account_id: accountId,
  account_limits: z.record(z.unknown()).nullish(),
  created_at: z.union([z.date(), z.string()]).nullish(),
  id: z.string(),
  aml_fraud_risk: NullableRiskValue,
  enhanced_monitoring: z.boolean().nullish(),
  excessive_program: z.boolean().nullish(),
  fraud_score: z.number().nullish(),
  mcc_category_risk: NullableRiskValue,
  monitoring_programs: z.array(MonitoringPrograms).nullish(),
  udaap_risk: NullableRiskValue,
  removed_at: z.union([z.date(), z.string()]).nullish(),
  updated_at: z.union([z.date(), z.string()]).nullish(),
});

export type AccountRiskElementResponse = z.infer<typeof AccountRiskElementResponse>;
