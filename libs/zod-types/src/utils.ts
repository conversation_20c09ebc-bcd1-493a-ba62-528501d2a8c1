import { ForwardError } from '@dbd/errors';
import {
  Auth<PERSON>ser<PERSON>ontext,
  BadRequestError,
  ForbiddenError,
  isHTTPError,
  NotFoundError,
  NotImplementedError,
  ResourceContext,
  ServerError,
  ServerErrorResponse,
  UnauthorizedError,
} from '@dbd/zod-types-common';
import { TsRestHttpError } from '@ts-rest/serverless/next';
import { AxiosError } from 'axios';

import { FilterOperator, TypedFilterCondition } from './pagination.js';

export const mapForwardError = (error: ForwardError) => {
  if (error.statusCode === 400) {
    return new BadRequestError({ cause: error });
  }
  if (error.statusCode === 401) {
    return new UnauthorizedError({ cause: error });
  }
  if (error.statusCode === 403) {
    return new ForbiddenError({ cause: error });
  }
  if (error.statusCode === 404) {
    return new NotFoundError({ cause: error });
  }
  if (error.statusCode === 501) {
    return new NotImplementedError({ cause: error });
  }

  return new ServerError({ cause: error });
};

export const mapAxiosError = (error: AxiosError) => {
  if (error.response?.status === 400) {
    return new BadRequestError({ cause: error });
  }
  if (error.response?.status === 401) {
    return new UnauthorizedError({ cause: error });
  }
  if (error.response?.status === 403) {
    return new ForbiddenError({ cause: error });
  }
  if (error.response?.status === 404) {
    return new NotFoundError({ cause: error });
  }
  if (error.response?.status === 501) {
    return new NotImplementedError({ cause: error });
  }

  return new ServerError({ cause: error });
};

export const mapTsRestError = (error: TsRestHttpError) => {
  if (error.statusCode === 400) {
    return new BadRequestError({ cause: error });
  }
  if (error.statusCode === 401) {
    return new UnauthorizedError({ cause: error });
  }
  if (error.statusCode === 403) {
    return new ForbiddenError({ cause: error });
  }
  if (error.statusCode === 404) {
    return new NotFoundError({ cause: error });
  }
  if (error.statusCode === 501) {
    return new NotImplementedError({ cause: error });
  }

  return new ServerError({ cause: error });
};

// Converts exceptions to value types
export const catchServiceError = (err: unknown, logger?: { error: (error: unknown) => void } | undefined) => {
  const error = (() => {
    if (err instanceof TsRestHttpError) {
      return mapTsRestError(err);
    }

    if (err instanceof ForwardError) {
      return mapForwardError(err);
    }

    if (err instanceof AxiosError) {
      return mapAxiosError(err);
    }

    return err;
  })();

  if (logger) {
    logger.error({ err: error });
  }

  if (isHTTPError(error)) {
    return error.toResponse();
  }

  const defaultResponse = ServerErrorResponse.parse({
    type: 'about:blank',
    title: 'Internal Server Error',
    detail: 'Internal Server Error',
    status: 500,
    code: '500-01',
    instance: 'Unknown',
  });

  return { status: 500 as const, body: defaultResponse };
};

export const applyResourceFilters = <T extends ResourceContext>(
  filterBy: TypedFilterCondition<T>[][],
  authUser: AuthUserContext,
  resourceContext?: ResourceContext,
  validResourceContexts: (keyof ResourceContext)[] = [
    'enterprise_id',
    'tenant_id',
    'partner_id',
    'business_id',
    'account_id',
  ],
) => {
  const filters = [...filterBy];

  if (authUser.enterprise_id && validResourceContexts.includes('enterprise_id')) {
    filters.push([['enterprise_id', FilterOperator.eq, authUser.enterprise_id] as TypedFilterCondition<T>]);
  }
  if (authUser.tenant_id && validResourceContexts.includes('tenant_id')) {
    filters.push([['tenant_id', FilterOperator.eq, authUser.tenant_id] as TypedFilterCondition<T>]);
  }
  if (authUser.partner_id && validResourceContexts.includes('partner_id')) {
    filters.push([['partner_id', FilterOperator.eq, authUser.partner_id] as TypedFilterCondition<T>]);
  }
  if (authUser.business_id && validResourceContexts.includes('business_id')) {
    filters.push([['business_id', FilterOperator.eq, authUser.business_id] as TypedFilterCondition<T>]);
  }

  if (resourceContext?.enterprise_id && validResourceContexts.includes('enterprise_id')) {
    filters.push([['enterprise_id', FilterOperator.eq, resourceContext.enterprise_id] as TypedFilterCondition<T>]);
  }
  if (resourceContext?.tenant_id && validResourceContexts.includes('tenant_id')) {
    filters.push([['tenant_id', FilterOperator.eq, resourceContext.tenant_id] as TypedFilterCondition<T>]);
  }
  if (resourceContext?.partner_id && validResourceContexts.includes('partner_id')) {
    filters.push([['partner_id', FilterOperator.eq, resourceContext.partner_id] as TypedFilterCondition<T>]);
  }
  if (resourceContext?.business_id && validResourceContexts.includes('business_id')) {
    filters.push([['business_id', FilterOperator.eq, resourceContext.business_id] as TypedFilterCondition<T>]);
  }
  if (resourceContext?.account_id && validResourceContexts.includes('account_id')) {
    filters.push([['account_id', FilterOperator.eq, resourceContext.account_id] as TypedFilterCondition<T>]);
  }

  return filters as TypedFilterCondition<T>[][];
};
