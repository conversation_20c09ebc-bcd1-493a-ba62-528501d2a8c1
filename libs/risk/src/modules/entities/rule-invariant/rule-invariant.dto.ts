import z from 'zod';

import { parseData } from '../mapper-parse.js';
import { RuleReference } from './rule-reference.entity.js';
import { RuleViolation } from './rule-violation.entity.js';

const schemaRuleReference = z
  .object({
    id: z.string(),
    action: z.string(),
    category: z.string(),
    description: z.string(),
    name: z.string(),
    createdAt: z
      .union([z.string(), z.date()])
      .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString())),
    updatedAt: z
      .union([z.string(), z.date()])
      .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString())),
    removedAt: z
      .union([z.string(), z.date()])
      .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString()))
      .nullish(),
  })
  .transform((arg) => ({
    ...arg,
    created_at: arg.createdAt,
    updated_at: arg.updatedAt,
    removed_at: arg.removedAt,
  }));

const snakeCasePreprocess = (val: unknown) =>
  val === null
    ? null
    : String(val)
        .toLowerCase()
        // Replace spaces and special characters with underscores
        .replace(/[\s-]+/g, '_')
        // Remove leading or trailing underscores
        .replace(/^_+|_+$/g, '');

export const RuleReferenceRequestDTO = z.object({
  action: z.string().nullish(),
  category: z.string(),
  description: z.string(),
  name: z.preprocess(snakeCasePreprocess, z.string()),
});

export type RuleReferenceRequestDTO = z.infer<typeof RuleReferenceRequestDTO>;

export const RuleReferenceResponseDTO = z.object({
  id: z.string(),
  action: z.string(),
  category: z.string(),
  description: z.string(),
  name: z.string(),
  created_at: z.union([z.string(), z.date()]).transform((arg) => arg.toString()),
  removedAt: z
    .union([z.string(), z.date()])
    .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString()))
    .nullish(),
  updated_at: z.union([z.string(), z.date()]).transform((arg) => arg.toString()),
});

export type RuleReferenceResponseDTO = z.infer<typeof RuleReferenceResponseDTO>;

/**
 *
 * @param data - Rule Reference entity as an object or an array
 * @returns - DTO suitable for external (priviledged) usage
 *
 */
export function toRuleReferenceMapper(data: RuleReference | RuleReference[]) {
  if (Array.isArray(data)) {
    return data.map((entity) => parseData(entity, schemaRuleReference));
  }

  return [parseData(data, schemaRuleReference)];
}

const schemaRuleViolation = z
  .object({
    id: z.string(),
    accountId: z.string(),
    acknowledgedBy: z.string().optional().nullish(),
    orchestratorId: z.string(),
    ruleAction: z.string(),
    ruleCategory: z.string(),
    ruleDescription: z.string(),
    ruleID: z.string(),
    ruleName: z.string(),
    acknowledgedAt: z
      .union([z.string(), z.date()])
      .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString()))
      .nullish(),
    sleepUntil: z.union([z.string(), z.date()]).optional().nullable(),
    createdAt: z
      .union([z.string(), z.date()])
      .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString())),
    updatedAt: z
      .union([z.string(), z.date()])
      .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString())),
    removedAt: z
      .union([z.string(), z.date()])
      .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString()))
      .nullish(),
    triggeredAt: z
      .union([z.string(), z.date()])
      .transform((arg) => (arg instanceof Date ? arg.toISOString() : arg.toString()))
      .nullish(),
  })
  .transform((arg) => ({
    account_id: arg.accountId,
    acknowledged_at: arg.acknowledgedAt,
    acknowledged_by: arg.acknowledgedBy,
    created_at: arg.createdAt,
    id: arg.id,
    orchestrator_id: arg.orchestratorId,
    removed_at: arg.removedAt,
    rule_action: arg.ruleAction,
    rule_category: arg.ruleCategory,
    rule_description: arg.ruleDescription,
    rule_id: arg.ruleID,
    rule_name: arg.ruleName,
    sleep_until: arg.sleepUntil,
    triggered_at: arg.triggeredAt,
    updated_at: arg.updatedAt,
  }));

/**
 *
 * @param data - Rule Reference entity as an object or an array
 * @returns - DTO suitable for external (priviledged) usage
 *
 */
export function toRuleViolationMapper(data: RuleViolation | RuleViolation[]) {
  if (Array.isArray(data)) {
    return data.map((entity) => parseData(entity, schemaRuleViolation));
  }

  return [parseData(data, schemaRuleViolation)];
}
