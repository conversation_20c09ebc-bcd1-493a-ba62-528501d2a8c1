import { ErrorBase } from '@dbd/errors';
import { z, ZodError } from 'zod';

import { parseData } from '../../mapper-parse.js';
import { NullableRiskValue, OneRatio as ratioValue, RiskLevels } from '../risk-profile.dto.js';
import { CreditRiskElement, ListOptions } from './credit-risk.entity.js';

const accountId = z.string().startsWith('acct_', { message: 'a valid merchant account ID is required' });
const entityId = z.string().startsWith('cre_', { message: 'a valid credit risk entity ID is required' });

const schema = z
  .object({
    // Identifiers
    accountId: accountId,
    id: entityId,
    // Data
    annualVolumeRisk: RiskLevels.default(RiskLevels.Enum.NONE).nullish(),
    avgTicketRisk: RiskLevels.default(RiskLevels.Enum.NONE).nullish(),
    baselineRiskExposure: z.number().nullish(),
    chargebackRisk: RiskLevels.default(RiskLevels.Enum.NONE).nullish(),
    chargebackRiskExposure: z.number().nullish(),
    creditRisk: RiskLevels.default(RiskLevels.Enum.NONE).nullish(),
    creditRiskScore: z.number().nullish(),
    nonDeliveryRisk: RiskLevels.default(RiskLevels.Enum.NONE).nullish(),
    nonDeliveryRiskExposure: z.number().nullish(),
    partnerRiskFactor: z.number().nullish(),
    refundRisk: RiskLevels.default(RiskLevels.Enum.NONE).nullish(),
    refundRiskExposure: z.number().nullish(),
    secondSignOccurence: z.boolean().nullish(),
    totalRiskExposure: z.number().nullish(),
    // Standard Timestamps
    createdAt: z.union([z.date(), z.string()]).nullish(),
    removedAt: z.union([z.date(), z.string()]).nullish(),
    updatedAt: z.union([z.date(), z.string()]).nullish(),
  })
  .transform((arg) => ({
    // Identifiers
    id: arg.id,
    account_id: arg.accountId,
    // Data
    annual_volume_risk: arg.annualVolumeRisk,
    avg_ticket_risk: arg.avgTicketRisk,
    baseline_risk_exposure: arg.baselineRiskExposure,
    chargeback_risk_exposure: arg.chargebackRiskExposure,
    chargeback_risk: arg.chargebackRisk,
    credit_risk: arg.creditRisk,
    credit_risk_score: arg.creditRiskScore,
    non_delivery_risk_exposure: arg.nonDeliveryRiskExposure,
    non_delivery_risk: arg.nonDeliveryRisk,
    partner_risk_factor: arg.partnerRiskFactor,
    refund_risk_exposure: arg.refundRiskExposure,
    refund_risk: arg.refundRisk,
    second_sign_occurence: arg.secondSignOccurence,
    total_risk_exposure: arg.totalRiskExposure,
    // Standard Timestamps
    removed_at: arg.removedAt,
    created_at: arg.createdAt,
    updated_at: arg.updatedAt,
  }));

export const CreditRiskElementDTO = z.object({
  // Identifiers
  id: entityId,
  account_id: accountId,
  // Data
  annual_volume_risk: NullableRiskValue,
  avg_ticket_risk: NullableRiskValue,
  baseline_risk_exposure: z.number().nullish(),
  chargeback_risk_exposure: z.number().nullish(),
  chargeback_risk: NullableRiskValue,
  credit_risk_score: z.number().nullish(),
  credit_risk: NullableRiskValue,
  non_delivery_risk_exposure: z.number().nullish(),
  non_delivery_risk: NullableRiskValue,
  partner_risk_factor: z.number().nullish(),
  refund_rate: ratioValue.nullish(),
  refund_risk_exposure: z.number().nullish(),
  refund_risk: NullableRiskValue,
  second_sign_occurence: z.boolean().nullish(),
  total_risk_exposure: z.number().nullish(),
  // Standard Timestamps
  created_at: z.union([z.date(), z.string()]).nullish(),
  removed_at: z.union([z.date(), z.string()]).nullish(),
  updated_at: z.union([z.date(), z.string()]).nullish(),
});

export type CreditRiskElementDTO = z.infer<typeof CreditRiskElementDTO>;

export const CreditRiskElementPatchRequestDTO = z.object({
  account_id: accountId,
  id: entityId,
  // Every field is optional
  annual_volume_risk: NullableRiskValue,
  avg_ticket_risk: NullableRiskValue,
  baseline_risk_exposure: z.number().nullish(),
  chargeback_risk_exposure: z.number().nullish(),
  chargeback_risk: NullableRiskValue,
  credit_risk_score: z.number().nullish(),
  credit_risk: NullableRiskValue,
  non_delivery_risk_exposure: z.number().nullish(),
  non_delivery_risk: NullableRiskValue,
  partner_risk_factor: z.number().nullish(),
  refund_risk_exposure: z.number().nullish(),
  refund_risk: NullableRiskValue,
  second_sign_occurence: z.boolean().nullish(),
  total_risk_exposure: z.number().nullish(),
});

export const CreditRiskElementCreateRequestDTO = z.object({
  account_id: accountId,
  // Every field is optional (for now)
  annual_volume_risk: NullableRiskValue,
  avg_ticket_risk: NullableRiskValue,
  baseline_risk_exposure: z.number().nullish(),
  chargeback_risk_exposure: z.number().nullish(),
  chargeback_risk: NullableRiskValue,
  credit_risk_score: z.number().nullish(),
  credit_risk: NullableRiskValue,
  non_delivery_risk_exposure: z.number().nullish(),
  non_delivery_risk: NullableRiskValue,
  partner_risk_factor: z.number().nullish(),
  refund_risk_exposure: z.number().nullish(),
  refund_risk: NullableRiskValue,
  second_sign_occurence: z.boolean().nullish(),
  total_risk_exposure: z.number().nullish(),
});

export type CreditRiskElementCreateRequestDTO = z.infer<typeof CreditRiskElementCreateRequestDTO>;

export const CreditRiskElementModifyResponseDTO = z.object({});

/**
 *
 * @param data - Credit Risk entity as an object or an array
 * @returns - DTO suitable for external (priviledged) usage
 *
 */
export function toCreditRiskMapper(data: CreditRiskElement | CreditRiskElement[]) {
  try {
    if (Array.isArray(data)) {
      return data.map((entity) => parseData(entity, schema));
    }

    return [parseData(data, schema)];
  } catch (reason: unknown) {
    if (reason instanceof ZodError) {
      return { error: new ErrorBase({ name: 'BAD_REQUEST', message: reason.message, cause: reason.cause }) };
    }

    return {
      error: new ErrorBase({
        name: 'INTERNAL_SERVER_ERROR',
        message: reason instanceof Error ? reason.message : String(reason),
      }),
    };
  }
}

/**
 *
 * @param data - the filter parameter from SearchFilter is opaque.
 * This method coerces the field into
 * an object suitable for queries of _this_ entity
 * @returns
 */
export function fromFilter(data: unknown) {
  if (!data) {
    return {} as ListOptions;
  }
  let value: unknown = data;
  if (typeof data === 'string') {
    value = JSON.parse(data);
  }
  const { data: result, success } = ListOptions.safeParse(value);
  if (!success) return {} as ListOptions;
  return result;
}
