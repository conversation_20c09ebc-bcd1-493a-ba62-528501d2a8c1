import { SearchRequestOptions } from '@dbd/backend-kit/paginator/meta';
import { ErrorBase } from '@dbd/errors';
import { Selectable } from 'kysely';
import { Simplify } from 'type-fest';
import { z, ZodError } from 'zod';

import { parseDate } from '../../mapper-parse.js';
import { RiskLevels } from '../risk-profile.dto.js';

export interface CreditRiskElementResultFailure<T extends string = string, E extends Error = ErrorBase<T>> {
  error: E;
}

export class CreditRiskElement {
  // Identifiers
  // The entity will nevevr be identifiable externally until
  // an account Id is associated. This occurs based on events.
  accountId: string;
  id: string;
  // Data
  annualVolumeRisk?: RiskLevels | null;
  avgTicketRisk?: RiskLevels | null;
  baselineRiskExposure?: number | null;
  chargebackRisk?: RiskLevels | null;
  chargebackRate?: number | null;
  chargebackRiskExposure?: number | null;
  creditRisk?: RiskLevels | null;
  creditRiskScore?: number | null;
  nonDeliveryRisk?: RiskLevels | null;
  nonDeliveryRiskExposure?: number | null;
  partnerRiskFactor?: number | null;
  refundRisk?: RiskLevels | null;
  refundRiskExposure: number | null;
  results?: Record<string, unknown> | null;
  secondSignOccurence?: boolean | null;
  totalRiskExposure?: number | null;
  // Standard Timestamps
  createdAt: Date | null;
  removedAt: Date | null;
  updatedAt: Date | null;

  constructor(opts: EntityOptions) {
    const data = EntityOptions.parse(opts);

    this.accountId = data.accountId ?? '';
    this.id = data.id ?? '';
    this.annualVolumeRisk = data.annualVolumeRisk;
    this.avgTicketRisk = data.avgTicketRisk;
    this.baselineRiskExposure = data.baselineRiskExposure;
    this.chargebackRisk = data.chargebackRisk;
    this.chargebackRiskExposure = data.chargebackRiskExposure;
    this.creditRisk = data.creditRisk;
    this.creditRiskScore = data.creditRiskScore;
    this.nonDeliveryRisk = data.nonDeliveryRisk;
    this.nonDeliveryRiskExposure = data.nonDeliveryRiskExposure;
    this.partnerRiskFactor = data.partnerRiskFactor;
    this.refundRisk = data.refundRisk;
    this.refundRiskExposure = data.refundRiskExposure;
    this.results = data.results;
    this.secondSignOccurence = data.secondSignOccurence;
    this.totalRiskExposure = data.totalRiskExposure;

    this.createdAt = parseDate(opts.createdAt);
    this.removedAt = parseDate(opts.removedAt);
    this.updatedAt = parseDate(opts.updatedAt);
  }

  // method to enforce each entity deserializing
  // only the properties it wishes to expose.
  toJSON() {
    return {
      accountId: this.accountId,
      id: this.id,
      annualVolumeRisk: this.annualVolumeRisk,
      avgTicketRisk: this.avgTicketRisk,
      baselineRiskExposure: this.baselineRiskExposure,
      chargebackRisk: this.chargebackRisk,
      chargebackRiskExposure: this.chargebackRiskExposure,
      creditRisk: this.creditRisk,
      creditRiskScore: this.creditRiskScore,
      nonDeliveryRisk: this.nonDeliveryRisk,
      nonDeliveryRiskExposure: this.nonDeliveryRiskExposure,
      partnerRiskFactor: this.partnerRiskFactor,
      refundRisk: this.refundRisk,
      refundRiskExposure: this.refundRiskExposure,
      results: this.results,
      secondSignOccurence: this.secondSignOccurence,
      totalRiskExposure: this.totalRiskExposure,
      createdAt: this.createdAt,
      removedAt: this.removedAt,
      updatedAt: this.updatedAt,
    };
  }

  static async fetch(repo: CreditRiskElementRepository, opts: FetchOptions) {
    try {
      const queryFields = FetchOptions.parse(opts);
      return repo.fetchCreditRisks(queryFields);
    } catch (reason: unknown) {
      if (reason instanceof ErrorBase) {
        return { error: reason };
      }
      return { error: new Error(reason as string) };
    }
  }

  static async list(repo: CreditRiskElementRepository, opts: ListOptions) {
    try {
      const queryFields = ListOptions.parse(opts);
      return repo.listCreditRisks(queryFields);
    } catch (reason: unknown) {
      if (reason instanceof ErrorBase) {
        return { error: reason };
      }
      return { error: new Error(reason as string) };
    }
  }

  static async save(repo: CreditRiskElementRepository, opts: CreateOptions, authCtx: Record<string, unknown>) {
    try {
      const queryFields = CreateOptions.parse(opts);
      return repo.createCreditRisk(authCtx, queryFields);
    } catch (reason: unknown) {
      if (reason instanceof ZodError) {
        return { error: new ErrorBase({ name: 'BAD_REQUEST', message: reason.message, cause: reason.cause }) };
      }
      if (reason instanceof ErrorBase) {
        return { error: reason };
      }
      return { error: new Error(reason as string) };
    }
  }

  static async modify(repo: CreditRiskElementRepository, opts: PatchOptions, authCtx: Record<string, unknown>) {
    try {
      const queryFields = PatchOptions.parse(opts);
      return repo.modifyCreditRisk(authCtx, queryFields);
    } catch (reason: unknown) {
      if (reason instanceof ZodError) {
        return { error: new ErrorBase({ name: 'BAD_REQUEST', message: reason.message, cause: reason.cause }) };
      }
      if (reason instanceof ErrorBase) {
        return { error: reason };
      }
      return { error: new Error(reason as string) };
    }
  }
}

// Accessor Types | Validators
// Most validators strive to
// remain local concerns. Types
// are exported to communicate intent.
// Validations are an implementation
// detail.
const accountID = z.string().startsWith('acct_');
const creditRiskElementID = z.string().startsWith('cre_');

// This type is **only** useful for validating
// repository-to-entity. It can be used to derive
// or inform other types.
export const EntityOptions = z.object({
  // When an entity does not exist it cannot have an ID
  // so this is optional.
  id: creditRiskElementID.nullable(),
  accountId: accountID,
  annualVolumeRisk: RiskLevels.nullable(),
  avgTicketRisk: RiskLevels.nullable(),
  baselineRiskExposure: z.number().nullable(),
  chargebackRisk: RiskLevels.nullable(),
  chargebackRiskExposure: z.number().nullable(),
  creditRisk: RiskLevels.nullable(),
  creditRiskScore: z.number().nullable(),
  nonDeliveryRisk: RiskLevels.nullable(),
  nonDeliveryRiskExposure: z.number().nullable(),
  partnerRiskFactor: z.number().nullable(),
  refundRisk: RiskLevels.nullable(),
  refundRiskExposure: z.number().nullable(),
  results: z.record(z.unknown()).nullable(),
  secondSignOccurence: z.boolean().nullable(),
  totalRiskExposure: z.number().nullable(),
  createdAt: z.date().nullable(),
  removedAt: z.date().nullable(),
  updatedAt: z.date().nullable(),
});

export type EntityOptions = z.infer<typeof EntityOptions>;

// CRUD Validators describe options to mutate entities
// These are directly connected to request DTOs
export const CreateOptions = z.object({
  accountId: accountID,
  annualVolumeRisk: RiskLevels.nullish(),
  avgTicketRisk: RiskLevels.nullish(),
  baselineRiskExposure: z.number().nullish(),
  chargebackRisk: RiskLevels.nullish(),
  chargebackRiskExposure: z.number().nullish(),
  creditRisk: RiskLevels.nullish(),
  creditRiskScore: z.number().nullish(),
  nonDeliveryRisk: RiskLevels.nullish(),
  nonDeliveryRiskExposure: z.number().nullish(),
  partnerRiskFactor: z.number().nullish(),
  refundRisk: RiskLevels.nullish(),
  refundRiskExposure: z.number().nullish(),
  results: z.record(z.unknown()).nullish(),
  secondSignOccurence: z.boolean().nullish(),
  totalRiskExposure: z.number().nullish(),
});

export type CreateOptions = z.infer<typeof CreateOptions>;

export const PatchOptions = z.object({
  id: creditRiskElementID,
  accountId: accountID.nullish(),
  annualVolumeRisk: RiskLevels.nullish(),
  avgTicketRisk: RiskLevels.nullish(),
  baselineRiskExposure: z.number().nullish(),
  chargebackRisk: RiskLevels.nullish(),
  chargebackRiskExposure: z.number().nullish(),
  creditRisk: RiskLevels.nullish(),
  creditRiskScore: z.number().nullish(),
  nonDeliveryRisk: RiskLevels.nullish(),
  nonDeliveryRiskExposure: z.number().nullish(),
  partnerRiskFactor: z.number().nullish(),
  refundRisk: RiskLevels.nullish(),
  refundRiskExposure: z.number().nullish(),
  results: z.record(z.unknown()).nullish(),
  secondSignOccurence: z.boolean().nullish(),
  totalRiskExposure: z.number().nullish(),
});

export type PatchOptions = z.infer<typeof PatchOptions>;

export type CreateOrModifyResult = { id?: string; error?: never } | ({ id?: string } & CreditRiskElementResultFailure);

// This validator should be used to
// describe how to fetch an entity
// from the repository. Its directly
// connected to the request DTO.
const GetOptions = creditRiskElementID;

const FetchOptions = GetOptions;
export type FetchOptions = z.infer<typeof FetchOptions>;

// List options should **always**
// extend the SearchRequestOptions
// then include the filter items
// of interest to the entity.
export const ListOptions = SearchRequestOptions.extend({
  accountId: accountID.nullish(),
  full: z.boolean().nullish().default(false),
  latest: z.boolean().nullish().default(true),
}).transform((args) => ({
  accountId: args.accountId,
  // cursor: args.cursor,
  full: args.full,
  latest: args.latest,
  // limit: args.limit,
  // sort: args.sort,
}));

export type ListOptions = Simplify<z.infer<typeof ListOptions>>;

/**
 * The Credit Risk repository is used to achieve Dependency Inversion
 * Any method defined here requires dependents (like repositories)
 * to implement the method signature.
 */
export interface CreditRiskElementRepository {
  asEntity<T>(row: Selectable<T>): CreditRiskElement;
  createCreditRisk(authCtx: Record<string, unknown>, opts: CreateOptions): Promise<CreateOrModifyResult>;
  fetchCreditRisks(opts: FetchOptions): Promise<CreditRiskElement | undefined>;
  listCreditRisks(opts: ListOptions): Promise<CreditRiskElement[]>;
  modifyCreditRisk(authCtx: Record<string, unknown>, opts: PatchOptions): Promise<CreateOrModifyResult>;
}
