import * as fc from 'fast-check';
import { Selectable } from 'kysely';
import { beforeAll, describe, it } from 'vitest';
import { ZodFastCheck } from 'zod-fast-check';
import { createFixture } from 'zod-fixture';

import {
  AccountRiskElement,
  type AccountRiskElementRepository,
  CreateOptions,
  CreateOrModifyResult,
  EntityOptions,
  FetchOptions,
  ListOptions,
  type PatchOptions,
} from './merchant-account.entity.js';

describe('Merchant Account Risk Element: Entity', () => {
  let repo: AccountRiskElementRepository;

  beforeAll(async () => {
    repo = new FakeAccountRiskElementRepository();
  });

  it.concurrent('create a new account risk element when one does not exist', async ({ expect }) => {
    const accountRiskArbitrary = ZodFastCheck().inputOf(CreateOptions);
    fc.assert(
      fc.asyncProperty(accountRiskArbitrary, async (accountRiskElement) => {
        //@ts-expect-error the arbitrary adds unknown | unknown[] to the type which causes the type error
        await expect(AccountRiskElement.save(repo, accountRiskElement, {})).resolves.toHaveProperty('id');
      }),
    );
  });

  it.concurrent('lists account risk elements for a merchant', async ({ expect }) => {
    const fetchArbitrary = ZodFastCheck().inputOf(ListOptions);
    fc.assert(
      fc.asyncProperty(fetchArbitrary, async (listOptions) => {
        fc.pre(!(listOptions.account_id && listOptions.account_id.trim().length === 0));
        //@ts-expect-error https://github.com/colinhacks/zod/issues/3537
        await expect(AccountRiskElement.list(repo, listOptions)).resolves.toHaveLength(4);
      }),
    );
  });
});

class FakeAccountRiskElementRepository implements AccountRiskElementRepository {
  async createAccountRisk(_opts: CreateOptions): Promise<CreateOrModifyResult> {
    const entity = FakeAccountRiskElementRepository.generateEntity();
    return { id: entity.id };
  }

  async modifyAccountRisk(_opts: PatchOptions): Promise<CreateOrModifyResult> {
    const entity = FakeAccountRiskElementRepository.generateEntity();
    return { id: entity.id };
  }
  async fetchAccountRisk(_opts: FetchOptions): Promise<AccountRiskElement | undefined> {
    return FakeAccountRiskElementRepository.generateEntity();
  }

  async listAccountRisks(_opts: ListOptions): Promise<AccountRiskElement[]> {
    return [
      FakeAccountRiskElementRepository.generateEntity(),
      FakeAccountRiskElementRepository.generateEntity(),
      FakeAccountRiskElementRepository.generateEntity(),
      FakeAccountRiskElementRepository.generateEntity(),
    ];
  }

  asEntity<T>(_row: Selectable<T>): AccountRiskElement {
    return FakeAccountRiskElementRepository.generateEntity();
  }

  static generateEntity() {
    const input = createFixture(EntityOptions);
    return new AccountRiskElement({
      ...input,
      createdAt: new Date(),
      updatedAt: new Date(),
      removedAt: null,
    });
  }
}
