import { SearchRequestOptions } from '@dbd/backend-kit/paginator/meta';
import { ErrorBase } from '@dbd/errors';
import { Selectable } from 'kysely';
import { Simplify } from 'type-fest';
import { z, ZodError } from 'zod';

import { parseDate } from '../../mapper-parse.js';
import { RiskLevels } from '../risk-profile.dto.js';

export interface AccountRiskElementResultFailure<T extends string = string, E extends Error = ErrorBase<T>> {
  error: E;
}

export class AccountRiskElement {
  // Identifiers
  // The entity will nevevr be identifiable externally until
  // an account Id is associated. This occurs based on events.
  accountId: string;
  id: string;
  // Data
  amlFraudRisk?: RiskLevels | null;
  enhancedMonitoring?: boolean | null;
  excessiveProgram?: boolean | null;
  fraudScore?: number | null;
  mccCategoryRisk?: RiskLevels | null;
  monitoringPrograms?: MonitoringPrograms[] | null;
  udaapRisk?: RiskLevels | null;
  results?: Record<string, unknown> | null;
  // Standard Timestamps
  createdAt: Date | null;
  removedAt: Date | null;
  updatedAt: Date | null;

  constructor(opts: EntityOptions) {
    const data = EntityOptions.parse(opts);

    this.accountId = data.accountId ?? '';
    this.id = data.id ?? '';
    this.results = data.results;

    this.amlFraudRisk = data.amlFraudRisk;
    this.enhancedMonitoring = data.enhancedMonitoring;
    this.excessiveProgram = data.excessiveProgram;
    this.fraudScore = data.fraudScore;
    this.mccCategoryRisk = data.mccCategoryRisk;
    this.monitoringPrograms = data.monitoringPrograms;
    this.udaapRisk = data.udaapRisk;

    this.createdAt = parseDate(opts.createdAt);
    this.removedAt = parseDate(opts.removedAt);
    this.updatedAt = parseDate(opts.updatedAt);
  }

  // method to enforce each entity deserializing
  // only the properties it wishes to expose.
  toJSON() {
    return {
      accountId: this.accountId,
      id: this.id,
      amlFraudRisk: this.amlFraudRisk,
      enhancedMonitoring: this.enhancedMonitoring,
      excessiveProgram: this.excessiveProgram,
      fraudScore: this.fraudScore,
      mccCategoryRisk: this.mccCategoryRisk,
      monitoringPrograms: this.monitoringPrograms,
      udaapRisk: this.udaapRisk,
      results: this.results,
      createdAt: this.createdAt,
      removedAt: this.removedAt,
      updatedAt: this.updatedAt,
    };
  }

  static async fetch(repo: AccountRiskElementRepository, opts: FetchOptions) {
    try {
      const queryFields = FetchOptions.parse(opts);
      return repo.fetchAccountRisk(queryFields);
    } catch (reason: unknown) {
      if (reason instanceof ErrorBase) {
        return { error: reason };
      }
      return { error: new Error(reason as string) };
    }
  }

  static async list(repo: AccountRiskElementRepository, opts: ListOptions) {
    try {
      const queryFields = ListOptions.parse(opts);
      return repo.listAccountRisks(queryFields);
    } catch (reason: unknown) {
      if (reason instanceof ErrorBase) {
        return { error: reason };
      }
      return { error: new Error(reason as string) };
    }
  }

  static async save(repo: AccountRiskElementRepository, opts: CreateOptions, authCtx: Record<string, unknown>) {
    try {
      const queryFields = CreateOptions.parse(opts);
      return repo.createAccountRisk(authCtx, queryFields);
    } catch (reason: unknown) {
      if (reason instanceof ZodError) {
        return { error: new ErrorBase({ name: 'BAD_REQUEST', message: reason.message, cause: reason.cause }) };
      }
      if (reason instanceof ErrorBase) {
        return { error: reason };
      }
      return { error: new Error(reason as string) };
    }
  }

  static async modify(repo: AccountRiskElementRepository, opts: PatchOptions, authCtx: Record<string, unknown>) {
    try {
      const queryFields = PatchOptions.parse(opts);
      return repo.modifyAccountRisk(authCtx, queryFields);
    } catch (reason: unknown) {
      if (reason instanceof ZodError) {
        return { error: new ErrorBase({ name: 'BAD_REQUEST', message: reason.message, cause: reason.cause }) };
      }
      if (reason instanceof ErrorBase) {
        return { error: reason };
      }
      return { error: new Error(reason as string) };
    }
  }
}

const snakeCasePreprocess = (val: unknown) =>
  val === null
    ? null
    : String(val)
        .toLowerCase()
        // Replace spaces and special characters with underscores
        .replace(/[\s-]+/g, '_')
        // Remove leading or trailing underscores
        .replace(/^_+|_+$/g, '');

export const MonitoringPrograms = z.preprocess(snakeCasePreprocess, z.string());
type MonitoringPrograms = z.infer<typeof MonitoringPrograms>;

// Accessor Types | Validators
// Most validators strive to
// remain local concerns. Types
// are exported to communicate intent.
// Validations are an implementation
// detail.

const accountID = z.string().startsWith('acct_');
const accountRiskElementID = z.string().startsWith('are_');

// This type is **only** useful for validating
// repository-to-entity. It can be used to derive
// or inform other types.
export const EntityOptions = z.object({
  // When an entity does not exist it cannot have an ID
  // so this is optional.
  id: accountRiskElementID.nullable(),
  accountId: accountID.nullable(),
  amlFraudRisk: RiskLevels.nullable(),
  enhancedMonitoring: z.boolean().nullable(),
  excessiveProgram: z.boolean().nullable(),
  fraudScore: z.number().nullable(),
  mccCategoryRisk: RiskLevels.nullable(),
  monitoringPrograms: z.array(MonitoringPrograms).default([]),
  udaapRisk: RiskLevels.nullable(),
  results: z.record(z.unknown()),
  createdAt: z.date().nullable(),
  removedAt: z.date().nullable(),
  updatedAt: z.date().nullable(),
});

export type EntityOptions = z.infer<typeof EntityOptions>;

// CRUD Validators describe options to mutate entities
// These are directly connected to request DTOs
export const CreateOptions = z.object({
  accountId: accountID,
  amlFraudRisk: RiskLevels.nullish(),
  enhancedMonitoring: z.boolean().nullish(),
  excessiveProgram: z.boolean().nullish(),
  fraudScore: z.number().nullish(),
  mccCategoryRisk: RiskLevels.nullish(),
  monitoringPrograms: z.array(MonitoringPrograms).nullish(),
  udaapRisk: RiskLevels.nullish(),
  results: z.record(z.unknown()).nullish(),
});

export type CreateOptions = z.infer<typeof CreateOptions>;

export const PatchOptions = z.object({
  id: accountRiskElementID,
  accountId: accountID.optional(),
  amlFraudRisk: RiskLevels.nullish(),
  enhancedMonitoring: z.boolean().nullish(),
  excessiveProgram: z.boolean().nullish(),
  fraudScore: z.number().nullish(),
  mccCategoryRisk: RiskLevels.nullish(),
  monitoringPrograms: z.array(MonitoringPrograms).nullish(),
  udaapRisk: RiskLevels.nullish(),
  results: z.record(z.unknown()).nullish(),
});

export type PatchOptions = z.infer<typeof PatchOptions>;

export type CreateOrModifyResult = { id?: string; error?: never } | ({ id?: string } & AccountRiskElementResultFailure);

// This validator should be used to
// describe how to fetch an entity
// from the repository. Its directly
// connected to the request DTO.
const GetOptions = accountRiskElementID;

const FetchOptions = GetOptions;
export type FetchOptions = z.infer<typeof FetchOptions>;

// List options should **always**
// extend the SearchRequestOptions
// then include the filter items
// of interest to the entity.
export const ListOptions = SearchRequestOptions.extend({
  account_id: accountID.nullish(),
  full: z.boolean().nullish().default(false),
  latest: z.boolean().nullish().default(true),
}).transform((args) => ({
  accountId: args.account_id,
  // cursor: args.cursor,
  full: args.full,
  latest: args.latest,
  // limit: args.limit,
  // sort: args.sort,
}));

export type ListOptions = Simplify<z.infer<typeof ListOptions>>;

/**
 * The Account Risk repository is used to achieve Dependency Inversion
 * Any method defined here requires dependents (like repositories)
 * to implement the method signature.
 */
export interface AccountRiskElementRepository {
  asEntity<T>(row: Selectable<T>): AccountRiskElement;
  createAccountRisk(authCtx: Record<string, unknown>, opts: CreateOptions): Promise<CreateOrModifyResult>;
  fetchAccountRisk(opts: FetchOptions): Promise<AccountRiskElement | undefined>;
  listAccountRisks(opts: ListOptions): Promise<AccountRiskElement[]>;
  modifyAccountRisk(authCtx: Record<string, unknown>, opts: PatchOptions): Promise<CreateOrModifyResult>;
}
