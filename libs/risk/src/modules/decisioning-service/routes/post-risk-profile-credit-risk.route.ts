import { ProblemJSON } from '@dbd/errors';
import { initServer } from '@ts-rest/fastify';

import { asExternalError } from '../../entities/external-error.mapper.js';
import { CreditRiskElement } from '../../entities/risk-profile/credit-risk/credit-risk.entity.js';
import { CreditRiskElementRepository } from '../../entities/risk-profile/credit-risk/credit-risk.repository.js';
import { APIError, handleAPIError } from '../../providers/provider-errors.js';
import { serviceContract } from '../contract.js';

export const PostRiskProfileCreditRisk = (server: ReturnType<typeof initServer>) => {
  return server.route(serviceContract.postRiskProfileCreditRisk, async ({ request, reply, body }) => {
    const repo = new CreditRiskElementRepository(request.server.decisionStore, request.server.outboxer, request.log);
    const res = await CreditRiskElement.save(
      repo,
      {
        accountId: body.account_id,
        annualVolumeRisk: body.annual_volume_risk,
        avgTicketRisk: body.avg_ticket_risk,
        baselineRiskExposure: body.baseline_risk_exposure,
        chargebackRisk: body.chargeback_risk,
        chargebackRiskExposure: body.chargeback_risk_exposure,
        creditRisk: body.credit_risk,
        creditRiskScore: body.credit_risk_score,
        nonDeliveryRisk: body.non_delivery_risk,
        nonDeliveryRiskExposure: body.non_delivery_risk_exposure,
        partnerRiskFactor: body.partner_risk_factor,
        refundRisk: body.refund_risk,
        refundRiskExposure: body.refund_risk_exposure,
        secondSignOccurence: body.second_sign_occurence,
        totalRiskExposure: body.total_risk_exposure,
        // Always pass the raw object
        results: body,
      },
      request.user,
    );
    if (res.error) {
      request.log.error(res.error);
      const { status: errStatus, type: errType, title } = asExternalError(res.error.name);
      reply.header(...ProblemJSON);
      return handleAPIError(
        new APIError('something went wrong', {
          cause: {
            detail: res.error.message,
            instance: request.url,
            status: errStatus,
            title,
            type: errType,
          },
        }),
      );
    }
    return {
      body: {},
      status: 200,
    };
  });
};
