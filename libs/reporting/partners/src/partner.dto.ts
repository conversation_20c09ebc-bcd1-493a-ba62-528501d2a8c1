import 'zod-openapi/extend';

import { Readable } from 'node:stream';

import { PaginatedResult, TypedPaginationParams } from '@dbd/zod-types';
import { AuthenticatedHeaders, ResourceContext } from '@dbd/zod-types-common/common';
import { ListMeta, PartnerId, PartnerProcessingPlanId } from '@dbd/zod-types-common/constants';
import { DefaultResponses } from '@dbd/zod-types-common/errors';
import { initContract } from '@ts-rest/core';
import { z } from 'zod';

import {
  PartnerBankAccountRow,
  PartnerMccCodeRow,
  PartnerProcessingPlanRow,
  PartnerRow,
  PartnerUserRow,
} from './partner.types.js';

export const ListPartnersDto = PartnerRow;
export type ListPartnersDto = z.infer<typeof ListPartnersDto>;

export const ListPartnersRequest = TypedPaginationParams(PartnerRow, 'ListPartner').openapi({
  ref: 'ListPartnersRequest',
});
export type ListPartnersRequest = z.infer<typeof ListPartnersRequest>;

export const ListPartnersResponse = PaginatedResult(ListPartnersDto.strip()).strip().openapi({
  ref: 'ListPartnersResponse',
});
export type ListPartnersResponse = z.infer<typeof ListPartnersResponse>;

export const ListPartnerNamesResponse = PaginatedResult(
  PartnerRow.pick({ partner_id: true, partner_name: true }).strip(),
).openapi({
  ref: 'ListPartnerNamesResponse',
});
export type ListPartnerNamesResponse = z.infer<typeof ListPartnerNamesResponse>;

export const ListPartnersMetaRequest = ResourceContext;
export type ListPartnersMetaRequest = z.infer<typeof ListPartnersMetaRequest>;

export const ListPartnerProcessingPlansRequest = TypedPaginationParams(
  PartnerProcessingPlanRow,
  'ListPartnerProcessingPlans',
).openapi({
  ref: 'ListPartnerProcessingPlansRequest',
});
export type ListPartnerProcessingPlansRequest = z.infer<typeof ListPartnerProcessingPlansRequest>;

export const ListPartnerProcessingPlansResponse = PaginatedResult(PartnerProcessingPlanRow.strip()).strip().openapi({
  ref: 'ListPartnerProcessingPlansResponse',
});
export type ListPartnerProcessingPlansResponse = z.infer<typeof ListPartnerProcessingPlansResponse>;

export const ListPartnerProcessingPlanNamesResponse = PaginatedResult(
  PartnerProcessingPlanRow.pick({ id: true, name: true }).strip(),
).openapi({
  ref: 'ListPartnerProcessingPlanNamesResponse',
});
export type ListPartnerProcessingPlanNamesResponse = z.infer<typeof ListPartnerProcessingPlanNamesResponse>;

export const ListPartnerMccCodesRequest = TypedPaginationParams(PartnerMccCodeRow).openapi({
  ref: 'ListPartnerMccCodesRequest',
});
export type ListPartnerMccCodesRequest = z.infer<typeof ListPartnerMccCodesRequest>;

export const ListPartnerMccCodesResponse = PaginatedResult(PartnerMccCodeRow.strip()).openapi({
  ref: 'ListPartnerMccCodesResponse',
});
export type ListPartnerMccCodesResponse = z.infer<typeof ListPartnerMccCodesResponse>;

export const ListPartnerUsersResponse = PaginatedResult(PartnerUserRow.strip()).openapi({
  ref: 'ListPartnerUsersResponse',
});
export type ListPartnerUsersResponse = z.infer<typeof ListPartnerUsersResponse>;

export const ListPartnerBankAccountsResponse = PaginatedResult(PartnerBankAccountRow.strip()).openapi({
  ref: 'ListPartnerBankAccountsResponse',
});
export type ListPartnerBankAccountsResponse = z.infer<typeof ListPartnerBankAccountsResponse>;

const contract = initContract();
export const PartnerContract = contract.router({
  getPartnerById: {
    method: 'GET',
    path: '/partners/:partnerId',
    pathParams: z.object({
      partnerId: PartnerId,
    }),
    query: z.object({
      fields: z.array(PartnerRow.keyof()).optional().default([]),
    }),
    responses: {
      200: PartnerRow,
      ...DefaultResponses,
    },
    headers: AuthenticatedHeaders,
    summary: 'Get a single partner',
  },
  listPartners: {
    method: 'GET',
    path: '/partners',
    responses: {
      200: ListPartnersResponse,
      ...DefaultResponses,
    },
    query: ListPartnersRequest,
    headers: AuthenticatedHeaders,
    summary: 'List all partners',
  },
  listPartnerNames: {
    method: 'GET',
    path: '/partner-names',
    responses: {
      200: ListPartnerNamesResponse,
      ...DefaultResponses,
    },
    query: ListPartnersRequest,
    headers: AuthenticatedHeaders,
    summary: 'List all partner names and ids',
  },
  listPartnersMeta: {
    method: 'GET',
    path: '/partners-meta',
    responses: {
      200: ListMeta,
      ...DefaultResponses,
    },
    query: ListPartnersMetaRequest,
    headers: AuthenticatedHeaders,
    summary: 'Fetch facets for partners',
  },
  exportPartners: {
    method: 'GET',
    path: '/partners-export',
    responses: {
      200: contract.otherResponse({
        contentType: 'text/csv',
        body: contract.type<Readable>(),
      }),
      ...DefaultResponses,
    },
    query: ListPartnersRequest,
    headers: AuthenticatedHeaders,
    summary: 'Export all partners',
  },
  getPartnerProcessingPlanById: {
    method: 'GET',
    path: '/partners/processing-plans/:id',
    pathParams: z.object({
      id: PartnerProcessingPlanId,
    }),
    responses: {
      200: PartnerProcessingPlanRow,
      ...DefaultResponses,
    },
    headers: AuthenticatedHeaders,
    summary: 'Get a single partner processing plan',
  },
  listPartnerProcessingPlanNames: {
    method: 'GET',
    path: '/partners/:partnerId/processing-plan-names',
    pathParams: z.object({
      partnerId: PartnerId,
    }),
    responses: {
      200: ListPartnerProcessingPlanNamesResponse,
      ...DefaultResponses,
    },
    query: ListPartnerProcessingPlansRequest,
    headers: AuthenticatedHeaders,
    summary: 'List all partner processing plan names and ids',
  },
  listPartnerMccCodes: {
    method: 'GET',
    path: '/partners/:partnerId/mcc-codes',
    responses: {
      200: ListPartnerMccCodesResponse,
      ...DefaultResponses,
    },
    query: ListPartnerMccCodesRequest,
    pathParams: z.object({
      partnerId: PartnerId,
    }),
    headers: AuthenticatedHeaders,
    summary: 'List all partner mcc codes',
  },
});
