{"name": "@dbd/source", "version": "1.0.0", "scripts": {"watch": "nx watch --all -- nx run \\$NX_PROJECT_NAME:build", "start": "nx serve", "build": "nx build --configuration=production", "start:prod": "pm2 start dist/apps/api/main.js -i max", "test": "nx run-many --target=test", "lint": "nx run-many --target=lint", "e2e": "nx e2e", "affected:apps": "nx affected:apps", "affected:libs": "nx affected:libs", "affected:build": "nx affected:build", "affected:e2e": "nx affected:e2e", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "affected:dep-graph": "nx affected:dep-graph", "affected": "nx affected", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "update": "nx migrate latest", "workspace-schematic": "nx workspace-schematic", "dep-graph": "nx dep-graph", "help": "nx help", "prepare": "husky", "preinstall": "npx only-allow bun", "pkg:audit": "syncpack list", "pkg:audit-fix": "syncpack fix-mismatches", "cz": "cz", "run-local": "docker compose -f tools/local-execution/docker-compose.all.yaml up", "clear-tsc": "find . -type f -name '*.tsbuildinfo' -exec rm -f {} \\;", "clear-out-tsc": "find . -name 'out-tsc' -type d -prune -exec rm -rf '{}' +", "clear-dist": "find libs -path '*/node_modules' -prune -o -name 'dist' -type d -exec rm -rf '{}' +", "clear-node-modules": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +", "clear-vitest": "find . -name 'vitest.config.mts.*.mjs' -prune -exec rm -rf {} +", "clear-all": "bun run clear-node-modules && bun run clear-tsc && bun run clear-out-tsc && bun run clear-vitest && bun run clear-dist"}, "private": true, "devDependencies": {"@aws-cdk/aws-apigatewayv2-alpha": "2.114.1-alpha.0", "@aws-cdk/aws-apigatewayv2-integrations-alpha": "2.114.1-alpha.0", "@aws-sdk/client-dynamodb-streams": "3.816.0", "@aws-sdk/client-lambda": "3.816.0", "@aws-sdk/types": "3.804.0", "@aws-sdk/util-dynamodb": "3.816.0", "@babel/preset-react": "7.18.6", "@babel/preset-typescript": "7.12.13", "@commitlint/cli": "17.8.0", "@commitlint/config-conventional": "17.8.0", "@cypress/grep": "^4.1.0", "@dbd/nextjs-lambda": "github:dbdventures/nextjs-lambda", "@emotion/babel-plugin": "11.11.0", "@eslint/compat": "1.1.1", "@eslint/eslintrc": "2.1.1", "@eslint/js": "9.8.0", "@faker-js/faker": "9.0.0", "@fast-check/jest": "^1.8.1", "@ladle/react": "^4.0.3", "@next/bundle-analyzer": "^15.1.6", "@nx-tools/container-metadata": "6.1.1", "@nx-tools/nx-container": "6.7.1", "@nx/cypress": "20.6.4", "@nx/devkit": "20.6.4", "@nx/esbuild": "20.6.4", "@nx/eslint": "20.6.4", "@nx/eslint-plugin": "20.6.4", "@nx/jest": "20.6.4", "@nx/js": "20.6.4", "@nx/next": "20.6.4", "@nx/node": "20.6.4", "@nx/playwright": "20.6.4", "@nx/plugin": "20.6.4", "@nx/react": "20.6.4", "@nx/vite": "20.6.4", "@nx/web": "20.6.4", "@nx/webpack": "20.6.4", "@nx/workspace": "20.6.4", "@nxlv/python": "^20.5.1", "@playwright/test": "^1.52.0", "@prisma/nextjs-monorepo-workaround-plugin": "5.19.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@sentry/webpack-plugin": "3.4.0", "@smithy/util-stream": "^3.1.3", "@svgr/webpack": "8.1.0", "@swc-node/register": "1.10.9", "@swc/cli": "0.3.14", "@swc/core": "1.6.13", "@swc/jest": "0.2.36", "@testcontainers/localstack": "10.15.0", "@testcontainers/mysql": "10.15.0", "@testing-library/jest-dom": "6.4.5", "@testing-library/react": "16.1.0", "@testing-library/user-event": "14.5.2", "@types/applepayjs": "^14.0.6", "@types/archiver": "5.3.1", "@types/aws-lambda": "8.10.149", "@types/bun": "1.2.15", "@types/dotenv-flow": "3.2.0", "@types/express": "4.17.21", "@types/formidable": "3.4.5", "@types/google.maps": "3.54.10", "@types/googlepay": "^0.7.5", "@types/http-errors": "2.0.1", "@types/jest": "29.5.12", "@types/jsonpath": "0.2.4", "@types/lodash": "4.14.202", "@types/lodash.camelcase": "4.3.7", "@types/lodash.clonedeep": "4.5.7", "@types/lodash.forin": "4.4.7", "@types/lodash.get": "4.4.7", "@types/lodash.isempty": "4.4.7", "@types/lodash.isequal": "4.5.6", "@types/lodash.isnil": "4.0.7", "@types/lodash.isnull": "3.0.7", "@types/lodash.isnumber": "3.0.7", "@types/lodash.isobject": "3.0.7", "@types/lodash.isplainobject": "4.0.7", "@types/lodash.isstring": "4.0.7", "@types/lodash.isundefined": "3.0.7", "@types/lodash.kebabcase": "4.1.7", "@types/lodash.omit": "4.5.7", "@types/lodash.pickby": "4.6.7", "@types/lodash.startcase": "4.4.7", "@types/node": "20.12.7", "@types/pg": "^8.11.10", "@types/react": "18.3.1", "@types/react-copy-to-clipboard": "5.0.4", "@types/react-dom": "18.3.0", "@types/react-retool": "1.4.0", "@types/react-text-mask": "5.4.11", "@types/sanitize-html": "2.9.0", "@types/semver": "7.5.8", "@types/serverless": "3.12.22", "@types/ssh2-sftp-client": "9.0.2", "@types/text-mask-addons": "3.8.1", "@types/uuid": "9.0.0", "@typespec/compiler": "1.0.0-rc.0", "@typespec/http": "1.0.0-rc.0", "@typespec/json-schema": "1.0.0-rc.0", "@types/nodemailer": "6.4.17", "@typespec/openapi": "1.0.0-rc.0", "@typespec/openapi3": "1.0.0-rc.0", "@typespec/rest": "0.68.0", "@typespec/versioning": "0.68.0", "@vitest/coverage-v8": "3.1.1", "@vitest/ui": "3.1.1", "aws-cdk": "2.150.0", "aws-cdk-lib": "2.187.0", "aws-sdk-client-mock": "4.1.0", "babel-jest": "29.7.0", "babel-loader": "8.1.0", "commitizen": "^4.3.0", "constructs": "10.2.18", "cypress": "14.4.0", "cypress-plugin-api": "2.11.2", "cz-conventional-changelog": "3.3.0", "danger": "12.3.4", "deploy-aws-s3-cloudfront": "3.8.0", "ejs": "3.1.10", "envalid": "7.3.1", "esbuild": "0.21.2", "eslint": "^9.8.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "18.0.0", "eslint-config-next": "14.2.26", "eslint-config-prettier": "9.1.0", "eslint-plugin-cypress": "5.0.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-tailwindcss": "3.17.4", "eslint-plugin-unused-imports": "4.1.4", "fast-check": "3.23.2", "html-webpack-plugin": "5.5.0", "http-server": "14.1.1", "husky": "9.1.6", "infer-next-props-type": "0.1.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "29.7.0", "jest-extended": "4.0.2", "jest-mock-extended": "3.0.6", "jiti": "2.4.2", "jsdom": "26.0.0", "json-refs": "3.0.15", "json-schema-to-zod": "2.6.1", "jsonc-eslint-parser": "2.2.0", "kysely-codegen": "0.15.0", "lint-staged": "15.2.7", "markdownlint": "0.34.0", "msw": "2.7.3", "nodemon": "3.1.4", "nx": "20.6.4", "openapi-to-postmanv2": "4.25.0", "orval": "7.1.0", "pino-pretty": "11.2.1", "playwright-slack-report": "1.1.89", "prettier": "3.3.3", "prisma": "5.7.0", "react-test-renderer": "18.2.0", "semantic-release": "^22.0.5", "serverless": "3.39.0", "serverless-domain-manager": "7.4.0", "serverless-dynamodb-stream-arn-plugin": "0.0.7", "serverless-esbuild": "1.52.1", "serverless-localstack": "1.0.5", "serverless-logging-config": "^1.0.2", "serverless-offline": "14.3.2", "serverless-plugin-datadog": "5.70.0", "serverless-plugin-warmup": "^8.3.0", "serverless-prune-plugin": "^2.0.2", "serverless-step-functions": "3.13.1", "syncpack": "13.0.3", "tailwind-scrollbar": "^3.1.0", "testcontainers": "10.19.0", "ts-jest": "29.1.2", "ts-node": "10.9.1", "tsconfig-paths": "^4.2.0", "tshy": "^3.0.2", "tsx": "^4.11.0", "type-fest": "4.18.2", "typescript": "5.7.3", "typescript-eslint": "8.29.0", "url-loader": "3.0.0", "vite": "6.2.4", "vite-plugin-dts": "4.5.3", "vitest": "3.1.1", "vitest-mock-extended": "2.0.2", "webpack-filter-warnings-plugin": "^1.2.1", "whatwg-fetch": "^3.6.20", "yaml": "^2.4.2", "zod-fixture": "2.5.2"}, "dependencies": {"@abraham/reflection": "0.12.0", "@aws-sdk/client-dynamodb": "3.816.0", "@aws-sdk/client-kms": "3.816.0", "@aws-sdk/client-s3": "3.816.0", "@aws-sdk/client-secrets-manager": "3.816.0", "@aws-sdk/client-sfn": "3.816.0", "@aws-sdk/client-sns": "3.816.0", "@aws-sdk/client-sqs": "3.816.0", "@aws-sdk/lib-dynamodb": "3.816.0", "@babel/core": "7.22.0", "@babel/runtime": "7.26.10", "@chakra-ui/accordion": "2.1.11", "@chakra-ui/anatomy": "2.2.1", "@chakra-ui/avatar": "2.2.8", "@chakra-ui/breadcrumb": "2.1.5", "@chakra-ui/button": "2.0.18", "@chakra-ui/checkbox": "2.2.14", "@chakra-ui/form-control": "2.0.18", "@chakra-ui/icon": "3.0.16", "@chakra-ui/icons": "2.0.18", "@chakra-ui/input": "2.0.21", "@chakra-ui/layout": "2.1.18", "@chakra-ui/media-query": "2.0.4", "@chakra-ui/menu": "2.1.12", "@chakra-ui/modal": "2.2.11", "@chakra-ui/popover": "2.1.9", "@chakra-ui/pro-theme": "0.0.65", "@chakra-ui/provider": "2.2.2", "@chakra-ui/radio": "2.0.22", "@chakra-ui/react": "2.5.5", "@chakra-ui/select": "2.0.19", "@chakra-ui/spinner": "2.0.13", "@chakra-ui/styled-system": "2.8.0", "@chakra-ui/switch": "2.0.26", "@chakra-ui/system": "2.5.5", "@chakra-ui/table": "2.0.17", "@chakra-ui/tabs": "2.1.9", "@chakra-ui/tag": "3.0.0", "@chakra-ui/textarea": "2.0.19", "@chakra-ui/theme-tools": "2.0.17", "@chakra-ui/transition": "2.0.16", "@emotion/react": "11.11.1", "@emotion/server": "11.10.0", "@emotion/styled": "11.11.0", "@fastify/auth": "4.6.1", "@fastify/aws-lambda": "4.0.0", "@fastify/env": "4.4.0", "@fastify/http-proxy": "9.5.0", "@fastify/jwt": "8.0.0", "@fastify/multipart": "8.3.1", "@fastify/sensible": "5.6.0", "@fastify/swagger": "8.14.0", "@fastify/swagger-ui": "3.0.0", "@fastify/type-provider-json-schema-to-ts": "3.0.0", "@fastify/type-provider-typebox": "4.0.0", "@fontsource/inter": "4.5.14", "@fusionauth/node-client": "1.46.0", "@heroicons/react": "2.1.3", "@hookform/resolvers": "3.9.1", "@internationalized/date": "3.7.0", "@maskito/core": "3.2.1", "@maskito/react": "3.2.1", "@middy/core": "6.1.6", "@middy/http-json-body-parser": "6.1.6", "@middy/warmup": "6.1.6", "@opentelemetry/api": "1.9.0", "@opentelemetry/api-logs": "0.56.0", "@opentelemetry/auto-instrumentations-node": "0.54.0", "@opentelemetry/context-async-hooks": "1.26.0", "@opentelemetry/core": "1.29.0", "@opentelemetry/exporter-jaeger": "1.26.0", "@opentelemetry/exporter-logs-otlp-grpc": "0.56.0", "@opentelemetry/exporter-logs-otlp-http": "0.56.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.56.0", "@opentelemetry/exporter-metrics-otlp-http": "0.56.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.56.0", "@opentelemetry/exporter-trace-otlp-http": "0.56.0", "@opentelemetry/instrumentation": "0.56.0", "@opentelemetry/instrumentation-aws-lambda": "0.49.0", "@opentelemetry/instrumentation-aws-sdk": "0.48.0", "@opentelemetry/instrumentation-dns": "0.41.0", "@opentelemetry/instrumentation-grpc": "0.56.0", "@opentelemetry/instrumentation-http": "0.56.0", "@opentelemetry/instrumentation-mysql": "0.44.0", "@opentelemetry/instrumentation-net": "0.42.0", "@opentelemetry/instrumentation-pg": "0.49.0", "@opentelemetry/instrumentation-pino": "0.45.0", "@opentelemetry/instrumentation-undici": "0.9.0", "@opentelemetry/otlp-exporter-base": "0.56.0", "@opentelemetry/propagator-aws-xray": "1.26.0", "@opentelemetry/propagator-b3": "1.29.0", "@opentelemetry/resource-detector-aws": "1.5.2", "@opentelemetry/resources": "1.29.0", "@opentelemetry/sdk-logs": "0.56.0", "@opentelemetry/sdk-metrics": "1.29.0", "@opentelemetry/sdk-node": "0.56.0", "@opentelemetry/sdk-trace-base": "1.29.0", "@opentelemetry/sdk-trace-node": "1.29.0", "@opentelemetry/semantic-conventions": "1.28.0", "@opentelemetry/winston-transport": "0.10.0", "@pb33f/wiretap": "0.4.4", "@peculiar/webcrypto": "1.4.3", "@prisma/client": "5.7.0", "@radix-ui/react-avatar": "1.1.0", "@radix-ui/react-checkbox": "1.0.4", "@radix-ui/react-collapsible": "1.1.0", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-dropdown-menu": "2.0.6", "@radix-ui/react-icons": "1.3.0", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-progress": "1.1.0", "@radix-ui/react-radio-group": "1.2.1", "@radix-ui/react-scroll-area": "1.1.0", "@radix-ui/react-select": "2.0.0", "@radix-ui/react-separator": "1.0.3", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-switch": "1.1.1", "@radix-ui/react-tabs": "1.1.1", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-tooltip": "1.1.2", "@react-aria/datepicker": "3.11.3", "@react-stately/datepicker": "3.10.3", "@remixicon/react": "4.3.0", "@sentry/aws-serverless": "9.15.0", "@sentry/nextjs": "9.15.0", "@sentry/node": "9.15.0", "@sentry/opentelemetry": "9.15.0", "@sentry/profiling-node": "9.15.0", "@sentry/react": "9.15.0", "@sinclair/typebox": "0.32.35", "@smithy/node-http-handler": "3.1.4", "@smithy/smithy-client": "4.4.1", "@smithy/types": "4.3.1", "@swc/helpers": "0.5.12", "@t3-oss/env-core": "0.13.6", "@tanstack/react-query": "4.24.4", "@tanstack/react-query-devtools": "4.24.4", "@tanstack/react-table": "8.17.3", "@ts-rest/core": "3.52.1", "@ts-rest/fastify": "3.52.1", "@ts-rest/next": "3.52.1", "@ts-rest/open-api": "3.52.1", "@ts-rest/react-query": "3.52.1", "@ts-rest/serverless": "3.52.1", "@vercel/otel": "1.9.1", "@vis.gl/react-google-maps": "1.4.2", "@vitejs/plugin-react": "4.2.1", "ajv": "8.12.0", "archiver": "7.0.1", "autoprefixer": "10.4.19", "axios": "1.8.2", "chakra-react-select": "4.4.3", "chart.js": "4.2.1", "class-variance-authority": "0.7.0", "close-with-grace": "1.3.0", "clsx": "2.1.1", "cmdk": "1.0.0", "csv-stringify": "6.4.5", "currency.js": "2.0.4", "datadog-lambda-js": "7.101.0", "date-fns": "3.6.0", "date-fns-tz": "3.1.3", "dayjs": "1.11.7", "dayjs-plugin-utc": "0.1.2", "dotenv": "16.0.3", "dotenv-flow": "3.2.0", "dynamodb-toolbox": "0.9.2", "dynamodb-toolbox-v1": "npm:dynamodb-toolbox@1.3.5", "fast-xml-parser": "4.4.1", "fastify": "4.29.1", "fastify-auth0-verify": "2.1.1", "fastify-plugin": "4.5.1", "fastify-type-provider-zod": "2.0.0", "flagsmith": "3.24.0", "flagsmith-nodejs": "5.1.1", "fluent-json-schema": "4.1.0", "form-data": "4.0.0", "formidable": "3.5.0", "framer-motion": "7.5.3", "html-react-parser": "5.2.2", "http-errors": "2.0.0", "http-proxy": "1.18.1", "ioredis": "5.3.2", "iron-session": "8.0.1", "jose": "5.3.0", "js-convert-case": "4.2.0", "js-sha256": "0.11.0", "jsonpath": "1.1.1", "jsurl2": "2.2.0", "jwt-decode": "4.0.0", "ksuid": "3.0.0", "kysely": "0.27.5", "lodash": "4.17.21", "lodash.camelcase": "4.3.0", "lodash.capitalize": "4.2.1", "lodash.clonedeep": "4.5.0", "lodash.forin": "4.4.0", "lodash.get": "4.4.2", "lodash.isempty": "4.4.0", "lodash.isequal": "4.5.0", "lodash.isnil": "4.0.0", "lodash.isnull": "3.0.0", "lodash.isnumber": "3.0.3", "lodash.isobject": "3.0.2", "lodash.isplainobject": "4.0.6", "lodash.isstring": "4.0.1", "lodash.isundefined": "3.0.1", "lodash.kebabcase": "4.1.1", "lodash.omit": "4.5.0", "lodash.pick": "4.4.0", "lodash.pickby": "4.6.0", "lodash.startcase": "4.4.0", "lucide-react": "0.419.0", "luxon": "3.5.0", "marked": "7.0.3", "mysql2": "3.11.0", "next": "14.2.28", "next-auth": "4.24.11", "next-themes": "0.3.0", "node-cache": "5.1.2", "node-fetch": "3.3.2", "nodemailer": "7.0.3", "nuqs": "2.4.1", "oslo": "1.2.1", "pg": "8.13.1", "pino": "9.6.0", "pino-lambda": "4.4.0", "plaid": "23.0.0", "postcss": "8.4.38", "posthog-js": "1.215.1", "postinstall": "0.10.3", "prop-types": "15.8.1", "react": "18.3.1", "react-chartjs-2": "5.2.0", "react-copy-to-clipboard": "5.1.0", "react-currency-input-field": "3.9.0", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-hook-form": "7.41.5", "react-icons": "5.5.0", "react-idle-timer": "5.7.2", "react-plaid-link": "3.3.2", "react-retool": "1.4.0", "react-text-mask": "5.5.0", "react-toastify": "9.1.1", "recharts": "2.13.0", "recursive-diff": "1.0.9", "redis": "4.6.8", "sanitize-html": "2.13.0", "secure-compare": "3.0.1", "semver": "7.5.4", "serverless-plugin-lambda-dead-letter": "1.2.1", "sharp": "0.32.6", "slonik": "46.1.0", "slonik-interceptor-query-logging": "46.1.0", "snowflake-sdk": "2.0.3", "sqs-consumer": "8.2.0", "ssh2-sftp-client": "9.1.0", "svix": "1.25.0", "svix-react": "1.13.3", "tailwind-merge": "2.3.0", "tailwind-variants": "0.2.1", "tailwindcss": "3.4.3", "tailwindcss-animate": "1.0.7", "text-mask-addons": "3.8.0", "tinycolor2": "1.6.0", "tslib": "2.8.1", "tsyringe": "4.8.0", "undici": "7.2.3", "uuid": "9.0.0", "vaul": "0.9.2", "webpack": "5.96.1", "webpack-merge": "5.8.0", "xior": "0.5.5", "xlsx": "0.18.5", "yup": "0.32.11", "zod": "3.24.2", "zod-fast-check": "0.10.1", "zod-openapi": "2.19.0", "zod-to-json-schema": "3.24.5", "zustand": "4.5.2"}, "overrides": {"@aws-sdk/client-acm": "3.816.0", "@aws-sdk/core": "3.816.0", "@aws-sdk/client-sqs": "3.816.0", "@aws-sdk/client-api-gateway": "3.816.0", "@aws-sdk/client-apigatewayv2": "3.816.0", "@aws-sdk/client-cloudwatch-logs": "3.816.0", "@aws-sdk/client-cloudformation": "3.816.0", "@aws-sdk/client-cognito-identity": "3.816.0", "@aws-sdk/client-cognito-identity-provider": "3.816.0", "@aws-sdk/client-lambda": "3.816.0", "@aws-sdk/credential-provider-cognito-identity": "3.816.0", "@aws-sdk/credential-providers": "3.816.0", "@aws-sdk/credential-provider-ini": "3.816.0", "@aws-sdk/credential-provider-node": "3.816.0", "@aws-sdk/credential-provider-sso": "3.816.0", "@aws-sdk/client-dynamodb": "3.816.0", "@aws-sdk/client-eventbridge": "3.816.0", "@aws-sdk/client-iam": "3.816.0", "@aws-sdk/client-route-53": "3.816.0", "@aws-sdk/client-s3": "3.816.0", "@aws-sdk/client-sso": "3.816.0", "@aws-sdk/lib-dynamodb": "3.816.0", "mimic-fn": "^2.1.0", "wrap-ansi": "^6.2.0", "ansi-styles": "^4.3.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "lint-staged": {"{apps,libs}/**/*.{ts,tsx,md,scss,html,js,jsx}": ["bun run nx affected:lint --fix --files", "bun run nx format:write --files"]}, "engines": {"node": ">=20.0 < 21"}, "packageManager": "bun@1.2.15", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "workspaces": ["apps/*", "libs/**"]}